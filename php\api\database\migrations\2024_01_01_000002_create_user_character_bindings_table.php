<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建用户角色绑定表（最终优化版）
 * 整合了所有后续修复的内容，直接创建最终结构
 *
 * 🎭 功能说明：
 * ✅ 包含优化后的用户角色绑定字段
 * ✅ 移除了冗余字段，添加了实际需要的字段
 * ✅ 包含完整的索引和外键约束
 *
 * 📝 版本历史：
 * - v1.0: 原始版本（包含binding_type等冗余字段）
 * - v2.0: 最终整合版本（本版本）
 *   * 删除：binding_type, custom_settings, notes, storyboard_position_id, binding_context, metadata
 *   * 添加：binding_name, binding_reason, custom_config, is_favorite, user_rating, user_feedback
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_character_bindings', function (Blueprint $table) {
            // 基础字段
            $table->id()->comment('绑定ID');
            $table->bigInteger('user_id')->unsigned()->comment('用户ID，关联p_users表');
            $table->bigInteger('character_id')->unsigned()->comment('角色ID，关联p_character_library表');

            // 绑定信息
            $table->string('binding_name', 100)->nullable()->comment('绑定名称');
            $table->string('binding_reason', 200)->nullable()->comment('绑定原因');
            $table->json('custom_config')->nullable()->comment('自定义配置');

            // 使用统计
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->timestamp('last_used_at')->nullable()->comment('最后使用时间');
            $table->json('usage_stats')->nullable()->comment('使用统计');

            // 状态字段
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->boolean('is_favorite')->default(false)->comment('是否收藏');

            // 用户反馈
            $table->decimal('user_rating', 3, 2)->nullable()->comment('用户评分');
            $table->text('user_feedback')->nullable()->comment('用户反馈');

            $table->timestamps();
            
            // 索引
            $table->index('user_id', 'idx_binding_user');
            $table->index('character_id', 'idx_binding_character');
            $table->index(['user_id', 'character_id'], 'idx_user_character');
            $table->index('last_used_at', 'idx_binding_last_used');
            $table->index('is_favorite', 'idx_binding_favorite');
            $table->index('user_rating', 'idx_binding_user_rating');
            $table->index(['is_active', 'usage_count'], 'idx_binding_active_usage');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('character_id')->references('id')->on('character_library')->onDelete('cascade');
            
            // 唯一约束
            $table->unique(['user_id', 'character_id'], 'unique_user_character');
            
            $table->comment('用户角色绑定表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_character_bindings');
    }
};
