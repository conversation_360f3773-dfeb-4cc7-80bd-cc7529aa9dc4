<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 修改WebSocket会话表的connected_at字段为可空
 * 修复认证时connected_at应该为null，连接成功后才设置的问题
 */
class MakeConnectedAtNullableInWebsocketSessions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('websocket_sessions', function (Blueprint $table) {
            // 修改connected_at字段为可空
            $table->timestamp('connected_at')->nullable()->change()->comment('连接时间（认证时为null，连接成功后设置）');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('websocket_sessions', function (Blueprint $table) {
            // 恢复原来的设置
            $table->timestamp('connected_at')->useCurrent()->change()->comment('连接时间');
        });
    }
}
