<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建角色库表（最终优化版）
 * 整合了所有后续迁移的修改，直接创建最终结构
 *
 * 🎭 功能说明：
 * ✅ 使用枚举分类系统（人类/拟人动物/原生动物）
 * ✅ 使用 ai_prompt 字段存储AI生成的角色提示词
 * ✅ 移除了冗余的描述字段（description、personality、background、appearance）
 * ✅ 移除了复杂的分类关联系统（category_id）
 * ✅ 包含完整的索引和约束
 *
 * 📝 版本历史：
 * - v1.0: 原始版本（2024-01-01）
 *   * 包含冗余字段：description、personality、background、appearance
 *   * 使用复杂的分类关联：category_id -> character_categories表
 *   * 包含项目关联字段：project_id、source_file_id
 *
 * - v2.0: AI提示词优化（2024-08-20）
 *   * 删除：personality、background字段
 *   * 添加：ai_prompt字段（统一存储AI生成的角色提示词）
 *   * 迁移文件：2024_08_20_000001_modify_character_library_table.php
 *
 * - v3.0: 外观描述清理（2024-08-20）
 *   * 删除：appearance字段
 *   * 迁移文件：2024_08_20_000002_remove_appearance_from_character_library.php
 *
 * - v4.0: 角色描述清理（2024-08-20）
 *   * 删除：description字段
 *   * 迁移文件：2024_08_20_000003_remove_description_from_character_library.php
 *
 * - v5.0: 分类系统重构（2024-08-20）
 *   * 删除：character_categories表、category_id字段
 *   * 添加：category枚举字段（人类/拟人动物/原生动物）
 *   * 迁移文件：2024_08_20_000004_restructure_character_categories.php
 *
 * - v6.0: 索引结构优化（2024-08-20）
 *   * 优化：删除冗余索引，添加必需字段索引
 *   * 完善：name、category、gender、age_range、tags索引策略
 *   * 迁移文件：2024_08_20_000002_optimize_character_library_indexes.php
 *
 * - v7.0: 最终整合版本（本版本）
 *   * 整合：所有历史修改直接集成到表创建语句
 *   * 清理：删除历史迁移文件，避免迁移冲突
 *   * 优化：最终的表结构和索引配置
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('character_library', function (Blueprint $table) {
            // 基础字段
            $table->id()->comment('角色ID');
            $table->string('name', 100)->comment('角色名称');
            $table->enum('category', ['人类', '拟人动物', '原生动物'])
                  ->default('人类')
                  ->comment('角色分类');
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->comment('性别');
            $table->enum('age_range', [
                '婴幼儿(0-6岁)',
                '儿童(6-12岁)',
                '少年(12-18岁)',
                '青年(18-40岁)',
                '中年(40-65岁)',
                '老年(65岁以上)'
            ])->nullable()->comment('年龄范围');

            // AI生成字段
            $table->text('ai_prompt')->nullable()->comment('AI生成的角色提示词');

            // 媒体字段
            $table->string('avatar', 255)->nullable()->comment('角色头像');
            $table->json('images')->nullable()->comment('角色图片集合');
            $table->json('tags')->nullable()->comment('角色标签');

            // 状态字段
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->boolean('is_premium')->default(false)->comment('是否高级角色');
            $table->boolean('is_featured')->default(false)->comment('是否推荐角色');

            // 统计字段
            $table->integer('sort_order')->default(0)->comment('排序权重');
            $table->integer('binding_count')->default(0)->comment('绑定次数');
            $table->decimal('rating', 3, 2)->default(0.00)->comment('评分');
            $table->integer('rating_count')->default(0)->comment('评分次数');

            // 关联字段
            $table->bigInteger('created_by')->unsigned()->nullable()->comment('创建者ID，关联p_users表');

            // 时间戳
            $table->timestamps();

            // 基础查询索引（必需字段）
            $table->index('name', 'idx_character_name');                    // 角色名称搜索
            $table->index('category', 'idx_character_category');            // 分类筛选
            $table->index('gender', 'idx_character_gender');                // 性别筛选
            $table->index('age_range', 'idx_character_age_range');          // 年龄范围筛选

            // 状态查询索引
            $table->index('is_active', 'idx_character_active');             // 激活状态
            $table->index(['is_active', 'is_featured'], 'idx_character_active_featured'); // 激活+推荐组合查询

            // 排序索引
            $table->index('sort_order', 'idx_character_sort_order');        // 自定义排序
            $table->index('rating', 'idx_character_rating');                // 评分排序

            // 关联索引
            $table->index('created_by', 'idx_character_creator');           // 创建者查询

            // 外键约束
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');

            $table->comment('角色库表（最终优化版）');
        });

        // JSON 字段索引说明
        // tags 字段为 JSON 类型，MySQL 8.0.17+ 才支持多值索引
        // 标签搜索在应用层通过以下方式实现：
        // 1. 使用 JSON_CONTAINS(tags, '"标签名"') 进行精确匹配
        // 2. 使用 JSON_SEARCH(tags, 'one', '%关键词%') 进行模糊搜索
        // 3. 在应用层缓存热门标签以提高查询性能
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('character_library');
    }
};
