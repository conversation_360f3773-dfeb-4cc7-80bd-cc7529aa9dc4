<?php

namespace App\Console\Commands;

use Throwable;
use Swoole\Table;
use Swoole\Timer;
use Swoole\Coroutine;
use App\Enums\ApiCodeEnum;
use App\Exceptions\ApiException;
use Swoole\WebSocket\Server;
use Illuminate\Console\Command;
use App\WebSocket\HandleMessage;
use App\Services\PyApi\WebSocketService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class WebSocketServer extends Command
{
    /**
     * 命令名称
     * 支持两种模式：传统模式和现代会话模式
     * 执行命令 : php artisan websocket:serve
     * @var string
     */
    protected $signature = 'websocket:serve {--port=} {--host=} {--mode=}';

    /**
     * 命令描述
     * @var string
     */
    protected $description = '启动WebSocket服务 (支持传统模式和现代会话模式)';

    protected $webSocketService;

    public function __construct(WebSocketService $webSocketService)
    {
        parent::__construct();
        $this->webSocketService = $webSocketService;
    }

    public function handle()
    {
        $mode = $this->option('mode') ?: config('websocket.server.mode');
        $host = $this->option('host') ?: config('websocket.server.host');
        $port = $this->option('port') ?: config('websocket.server.port');

        $this->info("Starting WebSocket server on {$host}:{$port} in {$mode} mode");

        // 检查Swoole扩展
        if (!extension_loaded('swoole')) {
            $this->error('Swoole extension is not installed. Please install swoole extension first.');
            return 1;
        }

        if ($mode === 'modern') {
            return $this->handleModernMode($host, $port);
        } else {
            return $this->handleLegacyMode($host, $port);
        }
    }

    /**
     * 现代会话模式 (来自WebSocketServerCommand)
     */
    private function handleModernMode($host, $port)
    {
        try {
            // 记录服务器启动时间
            Cache::put('websocket_server_start_time', Carbon::now(), 86400);

            // 创建WebSocket服务器：以下报错是IDE无法识别Swoole类型
            /** @var mixed $server WebSocket服务器实例 */
            $server = new \Swoole\WebSocket\Server($host, $port);

            // 配置服务器
            $server->set([
                'worker_num' => config('websocket.performance.worker_num'),
                // 🔧 取消Swoole心跳超时机制，改为手动管理
                // 'heartbeat_check_interval' => config('websocket.performance.heartbeat_check_interval'),
                // 'heartbeat_idle_time' => config('websocket.performance.heartbeat_idle_time'),
                'max_connection' => config('websocket.performance.max_connection'),
                'package_max_length' => config('websocket.performance.package_max_length'),
                'enable_coroutine' => true,
                'log_file' => storage_path('logs/' . config('websocket.logging.file')),
                'log_level' => 4, // SWOOLE_LOG_INFO
            ]);

            // 监听连接打开事件
            $server->on('open', function ($server, $request) {
                $this->handleModernOpen($server, $request);
            });

            // 监听消息事件
            $server->on('message', function ($server, $frame) {
                $this->handleModernMessage($server, $frame);
            });

            // 监听连接关闭事件
            $server->on('close', function ($server, $fd) {
                $this->handleModernClose($server, $fd);
            });

            // 监听Worker启动事件，启动Redis订阅协程
            $server->on('workerstart', function ($server, $workerId) {
                if ($workerId === 0) { // 只在第一个Worker中启动Redis订阅
                    $this->startRedisSubscription($server);

                    // 🔧 优化：启动按session_id的心跳检查定时器 (每1分钟执行一次)
                    // 现代模式：按session_id精确检查心跳，支持同用户多连接
                    \Swoole\Timer::tick(60000, function () {
                        try {
                            // 获取所有活跃的WebSocket会话
                            $activeSessions = \App\Models\WebSocketSession::where('status', 'connected')
                                ->where('last_ping_at', '>', \Carbon\Carbon::now()->subMinutes(10)) // 10分钟内有心跳的会话
                                ->get();

                            $totalSessions = $activeSessions->count();
                            $timeoutSessions = 0;

                            // 🚀 修复：按session_id逐个检查心跳，避免误断同用户的其他连接
                            foreach ($activeSessions as $session) {
                                // 如果超过5分钟没有心跳，标记为超时
                                if ($session->last_ping_at && $session->last_ping_at->diffInMinutes(\Carbon\Carbon::now()) > 5) {
                                    // 只断开当前session_id的连接，不影响同用户的其他连接
                                    $session->markAsTimeout();
                                    $timeoutSessions++;

                                    echo "⏰ 现代模式会话超时: session_id={$session->session_id}, user_id={$session->user_id}, business_type={$session->business_type}\n";
                                }
                            }

                            echo "💓 现代模式批量心跳检查完成: 活跃会话数={$totalSessions}, 超时会话数={$timeoutSessions}\n";

                        } catch (\Exception $e) {
                            echo "❌ 现代模式批量心跳检查失败: " . $e->getMessage() . "\n";

                            \Illuminate\Support\Facades\Log::error('现代模式WebSocket批量心跳检查异常', [
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                                'timestamp' => \Carbon\Carbon::now()->format('c')
                            ]);
                        }
                    });
                }
            });

            $this->info("WebSocket server started successfully in modern mode!");
            $this->info("Listening on wss://{$host}:{$port}");
            $this->info("🔧 心跳机制: 已取消Swoole 60秒超时断开，改为手动管理连接状态");

            // 启动服务器
            $server->start();

        } catch (\Exception $e) {
            $this->error("Failed to start WebSocket server: " . $e->getMessage());
            Log::error('WebSocket服务器启动失败', ['error' => $e->getMessage()]);
            return 1;
        }
    }

    /**
     * 传统模式 (原有的SSL模式)
     */
    private function handleLegacyMode($host, $port)
    {
        // ======================= SSL配置 - 开始 =======================
        // 从配置文件获取SSL证书路径
        $ssl_cert_file = config('websocket.ssl.cert_file');
        $ssl_key_file = config('websocket.ssl.key_file');

        // 如果配置文件未配置，使用默认路径
        if (!$ssl_cert_file || !$ssl_key_file) {
            $basePath = __DIR__ . '/ssl';
            $ssl_cert_file = $basePath.'/api.tiptop.cn.pem';
            $ssl_key_file = $basePath.'/api.tiptop.cn.key';
        } else {
            // 检查是否为绝对路径，如果不是则转换为绝对路径
            if (!$this->isAbsolutePath($ssl_cert_file)) {
                $ssl_cert_file = base_path($ssl_cert_file);
            }
            if (!$this->isAbsolutePath($ssl_key_file)) {
                $ssl_key_file = base_path($ssl_key_file);
            }
        }

        // 检查证书文件是否存在，如果不存在则提示错误并退出
        if (!file_exists($ssl_cert_file) || !file_exists($ssl_key_file)) {
            $this->error("SSL certificate files not found!");
            $this->error("Cert file path: " . $ssl_cert_file);
            $this->error("Key file path:  " . $ssl_key_file);
            $this->info("Please configure SSL certificate paths in config/websocket.php file:");
            $this->info("'ssl' => [");
            $this->info("    'cert_file' => 'path/to/certificate.pem',");
            $this->info("    'key_file' => 'path/to/private.key',");
            $this->info("],");
            $this->info("Or use 'mkcert api.tiptop.cn' to generate them in the default location");
            return 1;
        }

        // 创建服务器时，第四个参数需要增加 SWOOLE_SSL 来启用加密：以下报错是IDE无法识别Swoole类型
        /** @var mixed $server SSL WebSocket服务器实例 */
        $server = new Server(
            $host,
            $port,
            3, // SWOOLE_PROCESS
            1 | 512 // SWOOLE_SOCK_TCP | SWOOLE_SSL
        );
        
        $server->set([
            // 添加SSL证书和密钥文件配置
            'ssl_cert_file' => $ssl_cert_file,
            'ssl_key_file'  => $ssl_key_file,

            // 你原来的配置
            'reload_async' => true,
            'max_wait_time' => 60,
            'open_eof_check' => true,
            'package_eof' => '}',
            // 🔧 取消Swoole心跳超时机制，改为手动管理
            // 'heartbeat_check_interval' => 25,// 心跳间隔，单位秒
            // 'heartbeat_idle_time' => 60,// 空闲时间，单位秒
        ]);
        // ======================= SSL配置 - 结束 =======================

        // 以下报错是IDE无法识别Swoole类型
        /** @var mixed $table Swoole内存表实例 */
        $table = new Table(1024 * 100);
        $table->column('user_id', Table::TYPE_INT);
        $table->column('session_id', Table::TYPE_STRING, 64);
        $table->column('ping_at', Table::TYPE_INT);            // 最后收到心跳的时间
        $table->create();

        $handle = new HandleMessage();

        $server->on('workerstart', function ($server, $worker_id) use($table, $handle)
        {
            if ($worker_id == 0) // 仅在worker0进程启动定时器和Redis订阅
            {
                // 启动Redis订阅协程
                $this->startRedisSubscription($server);

                // 🔧 修改：取消Swoole 60秒超时断开机制，改为手动管理连接状态
                // 客户端每30秒发送心跳，服务器通过定时清理管理无效会话
                $this->info("💓 心跳机制修改: 取消Swoole超时断开，改为手动管理连接状态");

                // 🔧 优化：定时清理无效会话状态（每1分钟检查一次，提升清理及时性）
                Timer::tick(60000, function () use ($server) {
                    $this->cleanupInvalidSessions($server);
                });

                // 🔧 修复：确保定时器只在Worker 0中执行，避免重复执行
                if ($server->worker_id === 0) {
                    Timer::tick(60000, function () use ($table, $server) {
                    try {
                        $sessionHeartbeats = [];  // 🔧 修复：改为按session_id记录心跳
                        $totalConnections = 0;

                        // 从缓存获取所有活跃连接的心跳数据
                        foreach ($server->connections as $fd) {
                            if ($server->exist($fd) && $server->isEstablished($fd)) {
                                $cacheKey = "websocket_heartbeat_{$fd}";
                                $cacheData = \Illuminate\Support\Facades\Cache::get($cacheKey);

                                if ($cacheData && isset($cacheData['session_id']) && isset($cacheData['ping_at'])) {
                                    $totalConnections++;
                                    $sessionId = (string) $cacheData['session_id'];
                                    $pingAt = (int) $cacheData['ping_at'];

                                    // 🚀 修复：按session_id记录心跳，支持同用户多连接
                                    $sessionHeartbeats[$sessionId] = $pingAt;
                                }
                            }
                        }

                        if (empty($sessionHeartbeats)) {
                            echo "💓 批量心跳更新: 无活跃会话连接 (总连接数: {$totalConnections})\n";
                            return;
                        }

                        // 🔧 修复：调用新的按session_id批量更新方法
                        $updatedCount = \App\Models\WebSocketSession::batchUpdateHeartbeatBySessionId($sessionHeartbeats);

                        // 🚀 新增：检查数据库中的活跃会话总数
                        $dbActiveCount = \App\Models\WebSocketSession::whereIn('status', ['connected', 'pending'])->count();

                        echo "💓 批量心跳更新完成: 总连接数={$totalConnections}, 会话数=" . count($sessionHeartbeats) . ", 更新会话数={$updatedCount}, 数据库活跃会话={$dbActiveCount}\n";

                        // 如果数据不一致，输出警告
                        if (count($sessionHeartbeats) != $dbActiveCount) {
                            echo "⚠️  数据不一致警告: 缓存会话数(" . count($sessionHeartbeats) . ") != 数据库活跃会话数({$dbActiveCount})\n";
                        }

                    } catch (\Exception $e) {
                        echo "❌ 批量心跳更新失败: " . $e->getMessage() . "\n";

                        \Illuminate\Support\Facades\Log::error('WebSocket批量心跳更新异常', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'timestamp' => \Carbon\Carbon::now()->format('c')
                        ]);
                    }
                });
                }
            }
        });

        $server->on('open', function ($server, $request) use ($table, $handle)
        {
            $this->info("🔵 Legacy模式连接建立: #" . $request->fd);

            // 🔒 基于Session的安全验证策略
            $sessionId = $request->get['session_id'] ?? null;

            if (!$sessionId) {
                $this->sendErrorAndClose($server, $request->fd, ApiCodeEnum::BAD_REQUEST, 'session_id参数缺失');
                return;
            }

            // 从数据库验证Session
            $dbSession = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            if (!$dbSession) {
                $this->error("❌ Session验证失败: Session不存在");
                $this->sendErrorAndClose($server, $request->fd, ApiCodeEnum::UNAUTHORIZED, 'Session不存在');
                return;
            }

            if ($dbSession->status === 'disconnected') {
                $this->error("❌ Session验证失败: Session已失效，请重新认证 (当前状态: {$dbSession->status})");
                $this->sendErrorAndClose($server, $request->fd, ApiCodeEnum::SESSION_CONFLICT, 'Session已失效，请重新认证');
                return;
            } elseif ($dbSession->status === 'connected') {
                // 🔧 修复：状态为connected且session_id相同的判断为断线重连
                $this->info("🔄 检测到断线重连: Session状态为connected，检查旧连接 (session_id: {$dbSession->session_id})");

                // 检查是否有旧的FD
                $existingFd = Cache::get("websocket_session_{$dbSession->session_id}");
                if ($existingFd && $server->exist($existingFd)) {
                    // 旧连接仍然活跃，拒绝新连接并推送错误消息
                    $this->info("🔄 断线重连失败: 旧连接FD={$existingFd}仍然活跃，拒绝新连接FD={$request->fd}");

                    // 向前端推送错误消息
                    $errorMessage = [
                        'type' => 'connection_rejected',
                        'code' => ApiCodeEnum::CONFLICT,
                        'message' => '会话已在其他地方连接',
                        'data' => [
                            'session_id' => $dbSession->session_id,
                            'existing_fd' => $existingFd,
                            'new_fd' => $request->fd,
                            'reason' => 'duplicate_connection'
                        ],
                        'timestamp' => Carbon::now()->format('c')
                    ];
                    $server->push($request->fd, json_encode($errorMessage));

                    $this->sendErrorAndClose($server, $request->fd, ApiCodeEnum::CONFLICT, '会话已在其他地方连接');
                    return;
                } else {
                    // 旧连接不活跃，允许重连并使用新的FD
                    if ($existingFd) {
                        $this->info("🔄 断线重连: 旧FD={$existingFd}已断开，使用新FD={$request->fd}");
                        // 清理旧FD的缓存
                        Cache::forget("websocket_fd_{$existingFd}");
                    } else {
                        $this->info("🔄 断线重连: 未找到旧FD缓存，使用新FD={$request->fd}");
                    }

                    // 允许重连，使用新的FD
                    $this->info("✅ 断线重连成功: session_id={$dbSession->session_id}, 新FD={$request->fd}");
                }
            } elseif ($dbSession->status !== 'pending') {
                $this->error("❌ Session验证失败: Session状态无效 (当前状态: {$dbSession->status})");
                $this->sendErrorAndClose($server, $request->fd, ApiCodeEnum::UNAUTHORIZED, 'Session状态无效');
                return;
            }

            // 构建sessionData用于后续处理
            $sessionData = [
                'user_id' => $dbSession->user_id,
                'client_type' => $dbSession->client_type,
                'business_type' => $dbSession->business_type,
                'connection_ip' => $dbSession->connection_ip,
                'user_agent' => $dbSession->user_agent,
                'supported_events' => $dbSession->subscribed_events ?? []
            ];

            if (!isset($sessionData['user_id']) || empty($sessionData['user_id'])) {
                $this->sendErrorAndClose($server, $request->fd, ApiCodeEnum::UNAUTHORIZED, 'Session中缺少用户信息');
                return;
            }

            // 验证Session是否过期
            if (isset($sessionData['expires_at'])) {
                $expiresAt = Carbon::parse($sessionData['expires_at']);
                if ($expiresAt->isPast()) {
                    $this->sendErrorAndClose($server, $request->fd, ApiCodeEnum::SESSION_EXPIRED, 'Session已过期');
                    return;
                }
            }

            // 获取已验证的用户ID
            $userId = $sessionData['user_id'];

            // 用户身份已通过Session验证获取

            $this->info("🔍 Legacy模式连接验证: session_id={$sessionId}, user_id={$userId}");

            try {
                // 🚀 使用统一的session验证逻辑
                $validationResult = $this->validateSessionForConnection($sessionId, $userId, $server, $request->fd);

                if (!$validationResult['success']) {
                    // 验证失败，已经在validateSessionForConnection中处理了响应
                    return;
                }

                $session = $validationResult['session'];
                $isReconnection = ($session->status === 'connected'); // 判断是否为重连

                // 更新会话状态为已连接
                $session->status = 'connected';
                $session->connected_at = Carbon::now();
                $session->last_ping_at = Carbon::now();
                $session->save();

                // 🔧 优化：在内存表中存储会话信息，简化心跳数据
                $tableData = [
                    'user_id' => $session->user_id,
                    'session_id' => $sessionId,
                    'ping_at' => time()            // 最后收到心跳的时间
                ];
                $table->set($request->fd, $tableData);

                // 在缓存中存储双向映射
                Cache::put("websocket_fd_{$request->fd}", $sessionId, 3600);
                Cache::put("websocket_session_{$sessionId}", $request->fd, 3600);

                // 🔄 向前端推送连接成功消息
                $connectionMessage = [
                    'type' => $isReconnection ? 'reconnection_success' : 'connection_success',
                    'data' => [
                        'session_id' => $sessionId,
                        'user_id' => $session->user_id,
                        'business_type' => $session->business_type,
                        'client_type' => $session->client_type,
                        'connected_at' => $session->connected_at->format('c'),
                        'is_reconnection' => $isReconnection,
                        'message' => $isReconnection ? '断线重连成功' : '连接建立成功'
                    ],
                    'timestamp' => Carbon::now()->format('c')
                ];

                $server->push($request->fd, json_encode($connectionMessage));

                $this->info("✅ Legacy模式会话连接成功: {$sessionId}, 用户ID: {$session->user_id}, FD: {$request->fd}, " .
                           ($isReconnection ? '断线重连' : '新连接'));

            } catch (\Exception $e) {
                $this->info("❌ 会话处理失败: " . $e->getMessage());
                $server->close($request->fd);
            }
        });

        $server->on('message', function ($server, $frame) use ($table, $handle) {
            $this->info("🔵 Legacy模式收到消息 from #" . $frame->fd . ": " . $frame->data);

            // 检查是否是现代模式的心跳消息格式
            $modernPingCheck = json_decode($frame->data, true);
            if ($modernPingCheck && isset($modernPingCheck['type']) && $modernPingCheck['type'] === 'ping')
            {
                $this->info("💓 检测到现代模式心跳消息，处理中...");

                // 🔧 修复：收到现代模式ping时，更新心跳时间并清除等待状态
                $sessionData = $table->get($frame->fd);

                if ($sessionData)
                {
                    // 更新心跳时间
                    $sessionData['ping_at'] = time();
                    $table->set($frame->fd, $sessionData);

                    // 同时将心跳数据存储到缓存
                    $cacheKey = "websocket_heartbeat_{$frame->fd}";
                    $cacheData = [
                        'user_id' => $sessionData['user_id'],
                        'session_id' => $sessionData['session_id'],
                        'ping_at' => $sessionData['ping_at'],
                        'fd' => $frame->fd,
                        'updated_at' => time()
                    ];
                    Cache::put($cacheKey, $cacheData, 600); // 10分钟过期
                } else {
                    echo "❌ 现代模式ping处理失败，内存表中没有FD={$frame->fd}的数据\n";
                    echo "🔍 尝试遍历内存表查看所有数据:\n";
                    foreach ($table as $fd => $data) {
                        echo "   FD={$fd}: " . json_encode($data) . "\n";
                    }
                }

                // 🔧 优化：不再立即更新数据库，只记录日志
                try {
                    $sessionData = $table->get($frame->fd);
                    if ($sessionData && isset($sessionData['session_id'])) {
                        $sessionId = (string) $sessionData['session_id'];
                        $fd = (int) $frame->fd;
                        $pingTime = Carbon::createFromTimestamp($sessionData['ping_at'])->format('c');

                        $this->info("💓 Legacy模式心跳处理: session_id={$sessionId}, fd={$fd}, ping_at={$pingTime}");

                        // 发送现代模式格式的pong响应
                        $pongResponse = json_encode([
                            'event' => 'pong',
                            'message' => "内存表心跳时间更新成功: session_id={$sessionId}, ping_at={$pingTime}, fd={$fd}",
                            'data' => [
                                'server_time' => Carbon::now()->format('c'),
                            ]
                        ]);
                    } else {
                        // 会话数据不存在
                        $pongResponse = json_encode([
                            'event' => 'pong',
                            'message' => "会话数据不存在",
                            'data' => [
                                'server_time' => Carbon::now()->format('c'),
                            ]
                        ]);
                    }
                } catch (\Exception $e) {
                    // 发送现代模式格式的pong响应
                    $pongResponse = json_encode([
                        'event' => 'pong',
                        'message' => "心跳处理异常：". $e->getMessage(),
                        'data' => [
                            'server_time' => Carbon::now()->format('c'),
                        ]
                    ]);

                    $this->error("❌ Legacy模式心跳处理失败: " . $e->getMessage());
                }

                // 推送到前端
                $server->push($frame->fd, $pongResponse);
                return;
            }

            $event = '';
            $user_id = 0;
            $requests = [];
            $response = ['code' => ApiCodeEnum::SYSTEM_ERROR, 'message' => '发生错误', 'data' => []];
            try {
                $user_id = $table->get($frame->fd, 'user_id');
                list($event, $requests) = $handle->getRequestData($frame->data, $frame->fd);

                // 🔧 修复：处理pong响应，更新心跳状态
                if($event == 'pong') {
                    $existingData = $table->get($frame->fd);
                    if ($existingData) {
                        $existingData['ping_at'] = time();        // 更新最后收到pong的时间
                        $existingData['ping_waiting'] = 0;        // 清除等待状态
                        $existingData['ping_sent_at'] = 0;        // 清除发送时间
                        $table->set($frame->fd, $existingData);

                        $this->info("✅ 收到pong响应 FD={$frame->fd}，心跳时间已更新");
                    }
                    return false;
                }

                if(empty($event))
                {
                    return false;
                }
                // 所有WebSocket事件都直接调用对应的控制器，由控制器内部验证token
                list($class, $action) = $handle->event2Controller($event);
                $response = (new $class)->$action($requests);
            }
            catch (Throwable $e)
            {
                \Illuminate\Support\Facades\Log::error($e->getMessage().PHP_EOL.'file:'.$e->getFile().PHP_EOL.'line:'.$e->getLine());
                if($e instanceof ApiException)
                {
                    $response = ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => $e->getData()];
                } else {
                    $url = $event;
                    if(!empty($user_id))
                    {
                        $url .= ' @ ' . $user_id;
                    }
                    // 直接记录错误日志而不是使用队列
                    \Illuminate\Support\Facades\Log::error('WebSocket error', [
                        'url' => $url,
                        'message' => $e->getMessage(),
                        'params' => $frame->data,
                        'user_id' => $user_id
                    ]);
                    $response = ['code' => 500, 'message' => '发生错误', 'data' => []];
                }
            }
            $response['event'] = $event;
            $response['uid'] = $requests['uid'] ?? 0;
            $this->info("Push to #" . $frame->fd . ": " . json_encode($response));
            $server->push($frame->fd, $handle->encodeMessage($response));
        });

        $server->on('close', function ($server, $fd) use ($table) {
            $this->info('🔴 Legacy模式连接关闭: #' . $fd);

            // 获取会话ID
            $sessionId = Cache::get("websocket_fd_{$fd}");

            if ($sessionId) {
                try {
                    // 🔧 修复：检查会话状态，避免重复更新
                    $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
                    if ($session && $session->status === 'connected') {
                        // 只有当状态为connected时才更新为disconnected
                        // 如果已经是disconnected状态（主动断开），则不重复更新
                        $session->status = 'disconnected';
                        $session->disconnect_reason = '客户端断开连接';
                        $session->disconnected_at = Carbon::now();
                        $session->save();

                        $this->info("✅ Legacy模式会话已断开: {$sessionId}");
                    } else if ($session) {
                        $this->info("ℹ️ Legacy模式会话已处于断开状态: {$sessionId} (状态: {$session->status})");
                    }

                    // 清理双向映射缓存
                    Cache::forget("websocket_fd_{$fd}");
                    Cache::forget("websocket_session_{$sessionId}");

                    // 🔧 清理心跳缓存数据
                    $heartbeatCacheKey = "websocket_heartbeat_{$fd}";
                    Cache::forget($heartbeatCacheKey);

                    // 🚀 新增：清理Redis消息队列
                    $this->clearRedisMessageQueue($sessionId);

                    $this->info("💓 Legacy模式缓存已清理: fd={$fd}, session_id={$sessionId}");

                } catch (\Exception $e) {
                    $this->info("❌ 会话清理失败: " . $e->getMessage());
                }
            }

            if($table->exist($fd))
            {
                $user_id = $table->get($fd, 'user_id');
                $table->delete($fd);

                if(!empty($user_id))
                {
                    // 记录用户离线日志
                    \Illuminate\Support\Facades\Log::info('User offline', ['user_id' => $user_id]);
                }
            }
        });

        $this->info("WebSocket server started successfully in legacy mode!");
        $this->info("Listening on wss://{$host}:{$port}");
        $this->info("🔧 心跳机制: 已取消Swoole 60秒超时断开，改为手动管理连接状态");

        $server->start();
    }

    /**
     * 现代模式：处理连接打开
     */
    private function handleModernOpen($server, $request)
    {
        $fd = $request->fd;
        $sessionId = $request->get['session_id'] ?? null;

        if (!$sessionId) {
            $this->sendErrorAndClose($server, $fd, ApiCodeEnum::BAD_REQUEST, 'session_id参数缺失');
            return;
        }

        // 🔧 简化：直接基于数据库验证Session，取消缓存验证
        $this->info("🔍 Modern WebSocket Session验证调试信息:");
        $this->info("   - Session ID: {$sessionId}");
        $this->info("   - FD: {$fd}");

        // 从数据库验证Session
        $dbSession = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
        if (!$dbSession) {
            $this->error("❌ Modern Session验证失败: Session不存在");
            $this->sendErrorAndClose($server, $fd, ApiCodeEnum::UNAUTHORIZED, 'Session不存在');
            return;
        }

        if ($dbSession->status === 'disconnected') {
            $this->error("❌ Modern Session验证失败: Session已失效，请重新认证 (当前状态: {$dbSession->status})");
            $this->sendErrorAndClose($server, $fd, ApiCodeEnum::SESSION_CONFLICT, 'Session已失效，请重新认证');
            return;
        } elseif ($dbSession->status === 'connected') {
            // 🔧 修复：状态为connected且session_id相同的判断为断线重连
            $this->info("🔄 Modern模式检测到断线重连: Session状态为connected，检查旧连接 (session_id: {$dbSession->session_id})");

            // 检查是否有旧的FD
            $existingFd = Cache::get("websocket_session_{$dbSession->session_id}");
            if ($existingFd && $server->exist($existingFd)) {
                // 旧连接仍然活跃，拒绝新连接并推送错误消息
                $this->info("🔄 Modern模式断线重连失败: 旧连接FD={$existingFd}仍然活跃，拒绝新连接FD={$fd}");

                // 向前端推送错误消息
                $errorMessage = [
                    'type' => 'connection_rejected',
                    'code' => ApiCodeEnum::CONFLICT,
                    'message' => '会话已在其他地方连接',
                    'data' => [
                        'session_id' => $dbSession->session_id,
                        'existing_fd' => $existingFd,
                        'new_fd' => $fd,
                        'reason' => 'duplicate_connection'
                    ],
                    'timestamp' => Carbon::now()->format('c')
                ];
                $server->push($fd, json_encode($errorMessage));

                $this->sendErrorAndClose($server, $fd, ApiCodeEnum::CONFLICT, '会话已在其他地方连接');
                return;
            } else {
                // 旧连接不活跃，允许重连并使用新的FD
                if ($existingFd) {
                    $this->info("🔄 Modern模式断线重连: 旧FD={$existingFd}已断开，使用新FD={$fd}");
                    // 清理旧FD的缓存
                    Cache::forget("websocket_fd_{$existingFd}");
                } else {
                    $this->info("🔄 Modern模式断线重连: 未找到旧FD缓存，使用新FD={$fd}");
                }

                // 允许重连，使用新的FD
                $this->info("✅ Modern模式断线重连成功: session_id={$dbSession->session_id}, 新FD={$fd}");
            }
        } elseif ($dbSession->status !== 'pending') {
            $this->error("❌ Modern Session验证失败: Session状态无效 (当前状态: {$dbSession->status})");
            $this->sendErrorAndClose($server, $fd, ApiCodeEnum::UNAUTHORIZED, 'Session状态无效');
            return;
        }

        // 构建sessionData用于后续处理
        $sessionData = [
            'user_id' => $dbSession->user_id,
            'client_type' => $dbSession->client_type,
            'business_type' => $dbSession->business_type,
            'connection_ip' => $dbSession->connection_ip,
            'user_agent' => $dbSession->user_agent,
            'supported_events' => $dbSession->subscribed_events ?? []
        ];

        // 获取已验证的用户ID
        $userId = $sessionData['user_id'];

        // 🚀 新增：智能session_id验证逻辑
        $validationResult = $this->validateSessionForConnection($sessionId, $userId, $server, $fd);

        if (!$validationResult['success']) {
            // 验证失败，已经在validateSessionForConnection中处理了响应
            return;
        }

        $session = $validationResult['session'];
        $isReconnection = ($session->status === 'connected'); // 判断是否为重连

        // 更新会话状态和连接时间
        $session->status = 'connected';
        $session->connected_at = Carbon::now();
        $session->last_ping_at = Carbon::now();
        $session->save();

        // 存储连接映射
        Cache::put("websocket_fd_{$fd}", $sessionId, 3600);
        Cache::put("websocket_session_{$sessionId}", $fd, 3600);

        $this->info("Client connected: fd={$fd}, session={$sessionId}, user={$userId}, " .
                   ($isReconnection ? '断线重连' : '新连接'));
        Log::info('WebSocket客户端连接成功', [
            'fd' => $fd,
            'session_id' => $sessionId,
            'user_id' => $session->user_id,
            'connected_at' => $session->connected_at,
            'last_ping_at' => $session->last_ping_at,
            'is_reconnection' => $isReconnection
        ]);

        // 🔄 发送连接成功消息（区分新连接和重连）
        $server->push($fd, json_encode([
            'event' => $isReconnection ? 'reconnected' : 'connected',
            'type' => $isReconnection ? 'reconnection_success' : 'connection_success',
            'data' => [
                'session_id' => $sessionId,
                'user_id' => $session->user_id,
                'business_type' => $session->business_type,
                'client_type' => $session->client_type,
                'connected_at' => $session->connected_at->format('c'),
                'is_reconnection' => $isReconnection,
                'message' => $isReconnection ? '断线重连成功' : '连接建立成功',
                'server_time' => Carbon::now()->format('c')
            ]
        ]));
    }

    /**
     * 现代模式：处理消息
     */
    private function handleModernMessage($server, $frame)
    {
        $fd = $frame->fd;
        $data = $frame->data;

        // 添加详细的调试日志
        Log::info('🔵 WebSocket收到消息', [
            'fd' => $fd,
            'data' => $data,
            'timestamp' => Carbon::now()->format('c')
        ]);

        try {
            $message = json_decode($data, true);
            if (!$message) {
                Log::warning('❌ JSON解析失败', ['fd' => $fd, 'data' => $data]);
                return;
            }

            Log::info('✅ JSON解析成功', ['fd' => $fd, 'message' => $message]);

            $sessionId = Cache::get("websocket_fd_{$fd}");
            Log::info('🔍 查找会话ID', ['fd' => $fd, 'session_id' => $sessionId]);

            if (!$sessionId) {
                Log::warning('❌ 会话ID不存在，关闭连接', ['fd' => $fd]);
                $server->close($fd);
                return;
            }

            $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            Log::info('🔍 查找会话记录', ['session_id' => $sessionId, 'found' => !!$session]);

            if (!$session) {
                Log::warning('❌ 会话记录不存在，关闭连接', ['session_id' => $sessionId, 'fd' => $fd]);
                $server->close($fd);
                return;
            }

            $messageType = $message['type'] ?? '';
            Log::info('📝 处理消息类型', ['type' => $messageType, 'session_id' => $sessionId]);

            // 处理不同类型的消息
            switch ($messageType) {
                case 'ping':
                    Log::info('💓 处理心跳消息', ['session_id' => $sessionId, 'fd' => $fd]);
                    $this->handleModernPing($server, $fd, $session);
                    break;

                case 'subscribe':
                    $this->handleModernSubscribe($server, $fd, $session, $message['events'] ?? []);
                    break;

                case 'unsubscribe':
                    $this->handleModernUnsubscribe($server, $fd, $session, $message['events'] ?? []);
                    break;

                default:
                    Log::info('❓ 未知消息类型', ['type' => $messageType, 'session_id' => $sessionId]);
                    break;
            }

        } catch (\Exception $e) {
            Log::error('❌ WebSocket消息处理失败', [
                'fd' => $fd,
                'data' => $data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 🚀 智能session_id验证逻辑
     *
     * @param string $sessionId 会话ID
     * @param int $userId 用户ID
     * @param $server WebSocket服务器实例
     * @param int $fd 文件描述符
     * @return array 验证结果
     */
    private function validateSessionForConnection(string $sessionId, int $userId, $server, int $fd): array
    {
        // 1. 查找session_id对应的会话记录
        $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();

        if (!$session) {
            // 3. session_id不存在，按正常流程建立连接
            Log::info('WebSocket新会话连接', [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'fd' => $fd,
                'reason' => 'session_id不存在，创建新连接，支持同用户同业务类型并发连接'
            ]);

            // 创建新的会话记录
            $session = \App\Models\WebSocketSession::create([
                'session_id' => $sessionId,
                'user_id' => $userId,
                'client_type' => 'python_tool', // 默认类型
                'business_type' => 'unknown', // 待更新
                'status' => 'pending',
                'connection_ip' => $this->getClientIp($fd),
                'connected_at' => null,
                'last_ping_at' => \Carbon\Carbon::now(),
                'expires_at' => \Carbon\Carbon::now()->addMinutes(5),
                'message_count' => 0
            ]);

            return ['success' => true, 'session' => $session];
        }

        // session_id存在，进行进一步验证
        if ($session->user_id == $userId) {
            // 1. session_id归属当前用户
            if ($session->status === 'connected') {
                // 🔧 修复：使用正确的缓存键获取文件描述符
                $existingFd = \Illuminate\Support\Facades\Cache::get("websocket_session_{$sessionId}");

                if ($existingFd && $server->exist($existingFd)) {
                    // 已有活跃连接，推送提示信息
                    $this->sendActiveConnectionNotice($server, $fd, $sessionId);
                    return ['success' => false, 'reason' => 'already_connected'];
                } else {
                    // 会话记录显示连接但实际连接不存在，允许重新连接
                    Log::info('WebSocket会话重连', [
                        'session_id' => $sessionId,
                        'user_id' => $userId,
                        'fd' => $fd,
                        'existing_fd' => $existingFd,
                        'reason' => '会话记录存在但连接已断开，允许重连'
                    ]);
                    return ['success' => true, 'session' => $session];
                }
            } elseif ($session->status === 'disconnected') {
                // 🔧 修复：状态为disconnected的会话不支持重连，session_id已失效
                $disconnectReason = $session->disconnect_reason ?? '未知原因';
                $disconnectedAt = $session->disconnected_at ? $session->disconnected_at->format('Y-m-d H:i:s') : '未知时间';

                Log::warning('WebSocket尝试使用已失效的session_id', [
                    'session_id' => $sessionId,
                    'user_id' => $userId,
                    'fd' => $fd,
                    'status' => $session->status,
                    'disconnect_reason' => $disconnectReason,
                    'disconnected_at' => $disconnectedAt,
                    'business_type' => $session->business_type,
                    'client_type' => $session->client_type,
                    'reason' => 'session_id已失效，需要重新创建连接'
                ]);

                // 发送session_id冲突/失效错误
                $this->sendDetailedErrorAndClose($server, $fd, ApiCodeEnum::SESSION_CONFLICT, 'session_id已失效，无法重新连接', [
                    'session_status' => 'disconnected',
                    'disconnect_reason' => $disconnectReason,
                    'disconnected_at' => $disconnectedAt,
                    'business_type' => $session->business_type,
                    'client_type' => $session->client_type,
                    'error_type' => 'session_expired',
                    'suggestion' => '请重新获取有效的session_id并建立新连接'
                ]);

                return ['success' => false, 'reason' => 'session_expired'];
            } elseif ($session->status === 'pending') {
                // pending状态，正常的认证后连接流程
                Log::info('WebSocket会话正常连接', [
                    'session_id' => $sessionId,
                    'user_id' => $userId,
                    'fd' => $fd,
                    'old_status' => $session->status,
                    'reason' => '认证后正常建立连接，状态从pending转为connected'
                ]);
                return ['success' => true, 'session' => $session];
            } else {
                // 🔧 修复：其他状态（如timeout等）不允许连接，只允许pending状态
                Log::warning('WebSocket会话状态不允许连接', [
                    'session_id' => $sessionId,
                    'user_id' => $userId,
                    'fd' => $fd,
                    'current_status' => $session->status,
                    'reason' => '只允许pending状态的会话建立连接'
                ]);

                $this->sendDetailedErrorAndClose($server, $fd, ApiCodeEnum::UNAUTHORIZED, '会话状态不允许连接', [
                    'session_status' => $session->status,
                    'allowed_status' => 'pending',
                    'suggestion' => '请重新进行WebSocket认证获取新的session_id'
                ]);

                return ['success' => false, 'reason' => 'invalid_status'];
            }
        } else {
            // 2. session_id不归属当前用户，返回401错误
            $this->sendUnauthorizedError($server, $fd, $sessionId, $userId, $session->user_id);
            return ['success' => false, 'reason' => 'unauthorized'];
        }
    }

    /**
     * 发送活跃连接通知
     */
    private function sendActiveConnectionNotice($server, int $fd, string $sessionId): void
    {
        $message = [
            'type' => 'connection_notice',
            'code' => 200,
            'message' => '当前session_id已建立连接，处在活跃状态',
            'data' => [
                'session_id' => $sessionId,
                'status' => 'already_connected',
                'timestamp' => \Carbon\Carbon::now()->format('c')
            ]
        ];

        $server->push($fd, json_encode($message));

        Log::info('WebSocket发送活跃连接通知', [
            'session_id' => $sessionId,
            'fd' => $fd,
            'message' => $message['message']
        ]);

        // 发送通知后关闭新连接
        $server->close($fd);
    }

    /**
     * 发送未授权错误
     */
    private function sendUnauthorizedError($server, int $fd, string $sessionId, int $requestUserId, int $sessionUserId): void
    {
        $message = [
            'type' => 'error',
            'code' => ApiCodeEnum::UNAUTHORIZED,
            'message' => 'session_id不归属当前用户，连接被拒绝',
            'data' => [
                'session_id' => $sessionId,
                'request_user_id' => $requestUserId,
                'session_owner_id' => $sessionUserId,
                'timestamp' => \Carbon\Carbon::now()->format('c')
            ]
        ];

        $server->push($fd, json_encode($message));

        Log::warning('WebSocket未授权连接尝试', [
            'session_id' => $sessionId,
            'request_user_id' => $requestUserId,
            'session_owner_id' => $sessionUserId,
            'fd' => $fd,
            'message' => $message['message']
        ]);

        // 发送错误后关闭连接
        $server->close($fd);
    }

    /**
     * 发送错误信息并关闭连接
     */
    private function sendErrorAndClose($server, int $fd, int $code, string $message): void
    {
        $errorMessage = [
            'type' => 'error',
            'code' => $code,
            'message' => $message,
            'data' => [
                'timestamp' => \Carbon\Carbon::now()->format('c'),
                'reason' => $this->getErrorReason($code),
                'action' => $this->getErrorAction($code)
            ]
        ];

        // 🔧 改进：确保错误消息能被前端接收
        try {
            // 发送错误消息
            $jsonMessage = json_encode($errorMessage, JSON_UNESCAPED_UNICODE);
            $this->info("🚨 发送错误消息到前端: {$jsonMessage}");

            $result = $server->push($fd, $jsonMessage);
            if (!$result) {
                $this->error("❌ 错误消息发送失败: fd={$fd}");
            }

            // 延迟关闭连接，确保消息被发送
            usleep(100000); // 100ms延迟

        } catch (\Exception $e) {
            $this->error("❌ 发送错误消息异常: " . $e->getMessage());
        }

        $server->close($fd);

        Log::warning('WebSocket连接错误', [
            'fd' => $fd,
            'code' => $code,
            'message' => $message,
            'error_message' => $errorMessage
        ]);
    }

    /**
     * 获取错误原因说明
     */
    private function getErrorReason(int $code): string
    {
        $reasons = [
            ApiCodeEnum::BAD_REQUEST => '请求参数错误',
            ApiCodeEnum::UNAUTHORIZED => '身份验证失败',
            ApiCodeEnum::FORBIDDEN => '权限不足',
            ApiCodeEnum::NOT_FOUND => '资源不存在',
            ApiCodeEnum::CONFLICT => '状态冲突',
            ApiCodeEnum::TOO_MANY_REQUESTS => '请求过于频繁',
            ApiCodeEnum::ERROR => '服务器内部错误',
            ApiCodeEnum::SESSION_EXPIRED => '会话已过期',
            ApiCodeEnum::INVALID_CLIENT_TYPE => '客户端类型无效',
            ApiCodeEnum::CONNECTION_REJECTED => '连接被拒绝',
            ApiCodeEnum::WEBSOCKET_ERROR => 'WebSocket连接错误',
            ApiCodeEnum::SESSION_CONFLICT => 'session_id冲突或已失效'
        ];

        return $reasons[$code] ?? ApiCodeEnum::getDescription($code) ?? '未知错误';
    }

    /**
     * 获取错误处理建议
     */
    private function getErrorAction(int $code): string
    {
        $actions = [
            ApiCodeEnum::BAD_REQUEST => '请检查请求参数是否正确',
            ApiCodeEnum::UNAUTHORIZED => '请重新登录获取有效的session_id',
            ApiCodeEnum::FORBIDDEN => '请联系管理员获取相应权限',
            ApiCodeEnum::NOT_FOUND => '请确认请求的资源是否存在',
            ApiCodeEnum::CONFLICT => '请检查当前状态是否允许此操作',
            ApiCodeEnum::TOO_MANY_REQUESTS => '请稍后再试',
            ApiCodeEnum::ERROR => '请联系技术支持',
            ApiCodeEnum::SESSION_EXPIRED => '请重新登录获取新的session_id',
            ApiCodeEnum::INVALID_CLIENT_TYPE => '请使用正确的客户端类型连接',
            ApiCodeEnum::CONNECTION_REJECTED => '请检查连接参数或联系管理员',
            ApiCodeEnum::WEBSOCKET_ERROR => '请检查网络连接或重试',
            ApiCodeEnum::SESSION_CONFLICT => '请重新获取有效的session_id并建立新连接'
        ];

        return $actions[$code] ?? '请联系技术支持';
    }

    /**
     * 发送详细错误信息并关闭连接
     */
    private function sendDetailedErrorAndClose($server, int $fd, int $code, string $message, array $details = []): void
    {
        $errorMessage = [
            'type' => 'error',
            'code' => $code,
            'message' => $message,
            'data' => array_merge([
                'timestamp' => \Carbon\Carbon::now()->format('c'),
                'reason' => $this->getErrorReason($code),
                'action' => $this->getErrorAction($code)
            ], $details)
        ];

        // 🔧 确保错误消息能被前端接收
        try {
            $jsonMessage = json_encode($errorMessage, JSON_UNESCAPED_UNICODE);
            $this->info("🚨 发送详细错误消息到前端: {$jsonMessage}");

            $result = $server->push($fd, $jsonMessage);
            if (!$result) {
                $this->error("❌ 详细错误消息发送失败: fd={$fd}");
            }

            // 延迟关闭连接，确保消息被发送
            usleep(100000); // 100ms延迟

        } catch (\Exception $e) {
            $this->error("❌ 发送详细错误消息异常: " . $e->getMessage());
        }

        $server->close($fd);

        Log::warning('WebSocket详细连接错误', [
            'fd' => $fd,
            'code' => $code,
            'message' => $message,
            'details' => $details,
            'error_message' => $errorMessage
        ]);
    }



    /**
     * 检查心跳超时
     *
     * @param \App\Models\WebSocketSession $session 会话对象
     * @param int $fd 文件描述符
     * @return bool 是否有效（未超时）
     */
    private function checkHeartbeatTimeout($session, $fd): bool
    {
        try {
            // 1. 首先检查数据库中的last_ping_at
            if ($session->last_ping_at) {
                $lastPingMinutes = $session->last_ping_at->diffInMinutes(Carbon::now());
                if ($lastPingMinutes > 5) {
                    // 数据库中的心跳时间超过5分钟
                    return false;
                }
            }

            // 2. 检查缓存中的心跳时间（更实时）
            $heartbeatCacheKey = "websocket_heartbeat_{$fd}";
            $cacheData = Cache::get($heartbeatCacheKey);

            if ($cacheData && isset($cacheData['ping_at'])) {
                $lastPingTimestamp = (int) $cacheData['ping_at'];
                $currentTimestamp = time();
                $timeDiffMinutes = ($currentTimestamp - $lastPingTimestamp) / 60;

                if ($timeDiffMinutes > 5) {
                    // 缓存中的心跳时间超过5分钟
                    return false;
                }
            } else {
                // 没有心跳缓存数据，可能是异常情况
                return false;
            }

            // 心跳正常
            return true;

        } catch (\Exception $e) {
            Log::error('检查心跳超时失败', [
                'session_id' => $session->session_id,
                'fd' => $fd,
                'error' => $e->getMessage()
            ]);

            // 出现异常时，保守处理，认为连接有效
            return true;
        }
    }

    /**
     * 清理Redis消息队列
     */
    private function clearRedisMessageQueue(string $sessionId): void
    {
        try {
            $redis = app('redis');
            $listKey = "websocket:queue:{$sessionId}";

            // 获取队列长度
            $queueLength = $redis->llen($listKey);

            if ($queueLength > 0) {
                // 删除整个队列
                $redis->del($listKey);

                $this->info("🧹 WebSocket消息队列已清理: {$listKey} (清理了{$queueLength}条消息)");
            }

        } catch (\Exception $e) {
            $this->error("❌ 清理WebSocket消息队列失败: " . $e->getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private function getClientIp(int $fd): string
    {
        // 这里可以根据实际情况获取客户端IP
        return '127.0.0.1';
    }

    /**
     * 现代模式：处理连接关闭
     */
    private function handleModernClose($server, $fd)
    {
        $sessionId = Cache::get("websocket_fd_{$fd}");

        if ($sessionId) {
            // 🔧 修复：检查会话状态，避免重复更新
            $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            if ($session && $session->status === 'connected') {
                // 只有当状态为connected时才更新为disconnected
                // 如果已经是disconnected状态（主动断开），则不重复更新
                $session->disconnect('客户端断开连接');
                $this->info("✅ Modern模式会话已断开: {$sessionId}");
            } else if ($session) {
                $this->info("ℹ️ Modern模式会话已处于断开状态: {$sessionId} (状态: {$session->status})");
            }

            // 清理缓存
            Cache::forget("websocket_fd_{$fd}");
            Cache::forget("websocket_session_{$sessionId}");

            // 🔧 清理心跳缓存数据
            $heartbeatCacheKey = "websocket_heartbeat_{$fd}";
            Cache::forget($heartbeatCacheKey);

            // 🚀 新增：清理Redis消息队列
            $this->clearRedisMessageQueue($sessionId);

            $this->info("💓 Modern模式缓存已清理: fd={$fd}, session_id={$sessionId}");
        }

        $this->info("Client disconnected: fd={$fd}");
        Log::info('WebSocket客户端断开', ['fd' => $fd, 'session_id' => $sessionId]);
    }

    /**
     * 现代模式：处理心跳
     */
    private function handleModernPing($server, $fd, $session)
    {
        Log::info('💓 开始处理心跳', [
            'session_id' => $session->session_id,
            'fd' => $fd,
            'user_id' => $session->user_id,
            'current_last_ping_at' => $session->last_ping_at,
            'timestamp' => Carbon::now()->format('c')
        ]);

        try {
            // 🔧 优化：不再立即更新数据库，只记录日志
            Log::info('✅ 现代模式心跳处理 (内存表已更新)', [
                'session_id' => $session->session_id,
                'user_id' => $session->user_id,
                'fd' => $fd,
                'timestamp' => Carbon::now()->format('c')
            ]);

            // 发送pong响应
            $pongMessage = json_encode([
                'event' => 'pong',
                'data' => [
                    'server_time' => Carbon::now()->format('c')
                ]
            ]);

            $server->push($fd, $pongMessage);

            Log::info('🟢 发送pong响应成功', [
                'session_id' => $session->session_id,
                'fd' => $fd,
                'message' => $pongMessage,
                'timestamp' => Carbon::now()->format('c')
            ]);

        } catch (\Exception $e) {
            Log::error('❌ 心跳处理失败', [
                'session_id' => $session->session_id,
                'fd' => $fd,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => Carbon::now()->format('c')
            ]);
        }
    }

    /**
     * 现代模式：处理订阅事件
     */
    private function handleModernSubscribe($server, $fd, $session, $events)
    {
        foreach ($events as $event) {
            $session->subscribeEvent($event);
        }

        $server->push($fd, json_encode([
            'event' => 'subscribed',
            'data' => [
                'events' => $events,
                'subscribed_events' => $session->subscribed_events
            ]
        ]));
    }

    /**
     * 现代模式：处理取消订阅事件
     */
    private function handleModernUnsubscribe($server, $fd, $session, $events)
    {
        foreach ($events as $event) {
            $session->unsubscribeEvent($event);
        }

        $server->push($fd, json_encode([
            'event' => 'unsubscribed',
            'data' => [
                'events' => $events,
                'subscribed_events' => $session->subscribed_events
            ]
        ]));
    }

    /**
     * 检查路径是否为绝对路径
     */
    private function isAbsolutePath($path)
    {
        // Windows: 检查是否以盘符开头 (如 C:\ 或 D:\)
        if (PHP_OS_FAMILY === 'Windows' || strpos(PHP_OS, 'WIN') !== false) {
            return preg_match('/^[a-zA-Z]:[\\\\\/]/', $path);
        }
        // Unix/Linux: 检查是否以 / 开头
        return strpos($path, '/') === 0;
    }

    /**
     * 启动Redis订阅协程
     */
    private function startRedisSubscription($server): void
    {
        try {
            $this->info('🔍 开始启动Redis订阅协程...');
            Log::info('🔍 WebSocket服务器 - 开始启动Redis订阅协程');

            // 使用Laravel Redis门面，它会自动处理连接池和协程兼容性
            $this->info('🔍 使用Laravel Redis门面');
            Log::info('🔍 WebSocket服务器 - 使用Laravel Redis门面');

            $this->info('✅ Redis连接成功，启动订阅协程');
            Log::info('✅ WebSocket服务器 - Redis连接成功，启动订阅协程');

            // 启动协程订阅Redis通道
            Coroutine::create(function () use ($server) {
                $this->subscribeRedisChannelsWithLaravel($server);
            });

        } catch (\Exception $e) {
            $this->error('❌ Redis订阅协程启动失败: ' . $e->getMessage());
            Log::error('❌ WebSocket服务器 - Redis订阅协程启动失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 订阅Redis通道并处理消息
     */
    private function subscribeRedisChannels($redis, $server): void
    {
        try {
            $this->info('🔍 开始订阅Redis通道: websocket:push:* 和 websocket:admin:*');
            Log::info('🔍 WebSocket服务器 - 开始订阅Redis通道', [
                'patterns' => ['websocket:push:*', 'websocket:admin:*'],
                'redis_client' => get_class($redis)
            ]);

            // 根据Redis客户端类型选择不同的订阅方式
            if ($redis instanceof \Swoole\Coroutine\Redis) {
                // Swoole协程Redis客户端
                $this->subscribeSwooleRedis($redis, $server);
            } else {
                // 标准Redis扩展
                $this->subscribeStandardRedis($redis, $server);
            }

        } catch (\Exception $e) {
            $this->error('❌ Redis通道订阅失败: ' . $e->getMessage());
            Log::error('❌ WebSocket服务器 - Redis通道订阅失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 使用Swoole协程Redis客户端订阅
     */
    private function subscribeSwooleRedis($redis, $server): void
    {
        $result = $redis->psubscribe(['websocket:push:*', 'websocket:admin:*']);
        if (!$result) {
            throw new \Exception("Redis模式订阅失败: " . $redis->errMsg);
        }

        $this->info('✅ Swoole Redis模式订阅成功，开始监听消息...');
        Log::info('✅ WebSocket服务器 - Swoole Redis模式订阅成功');

        // 持续监听Redis消息
        while (true) {
            $message = $redis->recv();
            if ($message === false) {
                Log::error('❌ WebSocket服务器 - Redis接收消息失败', ['error' => $redis->errMsg]);
                break;
            }

            $this->processRedisMessage($message, $server);
        }
    }

    /**
     * 使用标准Redis扩展订阅
     */
    private function subscribeStandardRedis($redis, $server): void
    {
        $this->info('✅ 标准Redis模式订阅成功，使用定时器轮询模式...');
        Log::info('✅ WebSocket服务器 - 标准Redis模式订阅成功，使用定时器轮询');

        // 在Swoole环境中，使用定时器轮询Redis消息而不是阻塞订阅
        // 这样可以避免Redis连接在协程环境中的问题
        \Swoole\Timer::tick(1000, function () use ($redis, $server) {
            try {
                // 检查Redis连接状态
                if (!$redis->ping()) {
                    Log::warning('❌ WebSocket服务器 - Redis连接断开，尝试重连');
                    $redis->connect(
                        config('database.redis.default.host', '127.0.0.1'),
                        config('database.redis.default.port', 6379)
                    );
                    $redis->select(config('database.redis.default.database', 0));
                }

                // 使用BLPOP检查是否有新消息（这里我们改用其他方式）
                // 由于标准Redis扩展的psubscribe在Swoole中有问题，我们使用其他方式

            } catch (\Exception $e) {
                Log::error('❌ WebSocket服务器 - Redis轮询检查失败', [
                    'error' => $e->getMessage()
                ]);
            }
        });

        $this->info('✅ Redis定时器轮询已启动');
        Log::info('✅ WebSocket服务器 - Redis定时器轮询已启动');
    }

    /**
     * 使用Redis列表轮询消息
     */
    private function subscribeRedisChannelsWithLaravel($server): void
    {
        try {
            $this->info('🔍 开始使用Redis列表轮询消息');
            Log::info('🔍 WebSocket服务器 - 开始使用Redis列表轮询消息');

            // 启动定时器轮询Redis列表
            \Swoole\Timer::tick(100, function () use ($server) {
                try {
                    $this->pollRedisQueues($server);
                } catch (\Exception $e) {
                    Log::error('❌ WebSocket服务器 - Redis列表轮询失败', [
                        'error' => $e->getMessage()
                    ]);
                }
            });

            $this->info('✅ Redis列表轮询已启动');
            Log::info('✅ WebSocket服务器 - Redis列表轮询已启动');

        } catch (\Exception $e) {
            $this->error('❌ Redis列表轮询启动失败: ' . $e->getMessage());
            Log::error('❌ WebSocket服务器 - Redis列表轮询启动失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 轮询Redis队列获取消息
     */
    private function pollRedisQueues($server): void
    {
        try {
            // 获取所有活跃的WebSocket会话
            $activeSessions = \App\Models\WebSocketSession::active()->pluck('session_id');

            foreach ($activeSessions as $sessionId) {
                $listKey = "websocket:queue:{$sessionId}";

                // 使用RPOP从列表右侧弹出消息（FIFO）
                $redis = app('redis');
                $message = $redis->rpop($listKey);

                if ($message) {
                    Log::info('🔍 WebSocket服务器 - 从Redis列表获取消息', [
                        'session_id' => $sessionId,
                        'list_key' => $listKey,
                        'message_length' => strlen($message)
                    ]);

                    $this->processRedisMessageData($message, $server, $listKey);
                }
            }

        } catch (\Exception $e) {
            Log::error('❌ WebSocket服务器 - 轮询Redis队列失败', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理Redis消息（Swoole格式）
     */
    private function processRedisMessage($message, $server): void
    {
        try {
            // 处理订阅消息
            if (is_array($message) && count($message) >= 4) {
                $type = $message[0];      // 消息类型 (pmessage)
                $pattern = $message[1];   // 订阅模式
                $channel = $message[2];   // 实际通道
                $data = $message[3];      // 消息内容

                if ($type === 'pmessage') {
                    Log::info('🔍 WebSocket服务器 - 收到Redis消息', [
                        'pattern' => $pattern,
                        'channel' => $channel,
                        'message_length' => strlen($data)
                    ]);

                    $this->processRedisMessageData($data, $server, $channel);
                }
            }
        } catch (\Exception $e) {
            Log::error('❌ WebSocket服务器 - 处理Redis消息失败', [
                'message' => $message,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理Redis消息数据
     */
    private function processRedisMessageData($data, $server, string $channel = ''): void
    {
        try {
            $messageData = json_decode($data, true);
            if (!$messageData) {
                Log::warning('❌ WebSocket服务器 - Redis消息解析失败', ['message' => $data, 'channel' => $channel]);
                return;
            }

            // 🔧 新增：根据通道类型处理不同的消息
            if (str_starts_with($channel, 'websocket:admin:')) {
                $this->handleAdminMessage($messageData, $server, $channel);
                return;
            }

            // 原有的推送消息逻辑
            $sessionId = $messageData['session_id'] ?? null;
            $messageContent = $messageData['message'] ?? null;

            if (!$sessionId || !$messageContent) {
                Log::warning('❌ WebSocket服务器 - Redis消息格式错误', ['data' => $messageData, 'channel' => $channel]);
                return;
            }

            Log::info('✅ WebSocket服务器 - 准备推送消息到WebSocket连接', [
                'session_id' => $sessionId,
                'message_type' => $messageContent['event'] ?? 'unknown',
                'channel' => $channel
            ]);

            // 查找对应的WebSocket连接并推送消息
            $this->pushToWebSocketConnection($server, $sessionId, $messageContent);

        } catch (\Exception $e) {
            Log::error('❌ WebSocket服务器 - 处理Redis消息数据失败', [
                'data' => $data,
                'channel' => $channel,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理管理通道消息
     */
    private function handleAdminMessage(array $messageData, $server, string $channel): void
    {
        try {
            $type = $messageData['type'] ?? '';

            switch ($type) {
                case 'force_disconnect':
                    $this->handleForceDisconnect($messageData, $server);
                    break;

                default:
                    Log::warning('❌ WebSocket服务器 - 未知的管理消息类型', [
                        'type' => $type,
                        'channel' => $channel,
                        'data' => $messageData
                    ]);
                    break;
            }

        } catch (\Exception $e) {
            Log::error('❌ WebSocket服务器 - 处理管理消息失败', [
                'channel' => $channel,
                'data' => $messageData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理强制断开连接
     */
    private function handleForceDisconnect(array $messageData, $server): void
    {
        try {
            $sessionId = $messageData['session_id'] ?? '';
            $fd = $messageData['fd'] ?? 0;
            $reason = $messageData['reason'] ?? '管理员强制断开';

            if (!$sessionId || !$fd) {
                Log::warning('❌ WebSocket服务器 - 强制断开消息参数不完整', [
                    'session_id' => $sessionId,
                    'fd' => $fd
                ]);
                return;
            }

            // 检查连接是否存在
            if ($server->exist($fd)) {
                // 向客户端发送断开通知
                $disconnectMessage = [
                    'type' => 'force_disconnect',
                    'message' => $reason,
                    'timestamp' => Carbon::now()->format('c')
                ];

                $server->push($fd, json_encode($disconnectMessage));

                // 强制关闭连接
                $server->close($fd);

                Log::info('✅ WebSocket服务器 - 强制断开连接成功', [
                    'session_id' => $sessionId,
                    'fd' => $fd,
                    'reason' => $reason
                ]);
            } else {
                Log::info('🔍 WebSocket服务器 - 连接已不存在，无需强制断开', [
                    'session_id' => $sessionId,
                    'fd' => $fd
                ]);
            }

        } catch (\Exception $e) {
            Log::error('❌ WebSocket服务器 - 强制断开连接失败', [
                'data' => $messageData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 推送消息到WebSocket连接
     */
    private function pushToWebSocketConnection($server, string $sessionId, array $message): void
    {
        try {
            // 从缓存获取连接ID
            $connectionId = Cache::get("websocket_session_{$sessionId}");

            if (!$connectionId || !$server->exist($connectionId)) {
                Log::warning('WebSocket连接不存在', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId
                ]);

                // 尝试从数据库获取会话信息并清理无效会话
                $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
                if ($session && $session->status === 'connected') {
                    $session->status = 'disconnected';
                    $session->disconnect_reason = '连接已断开';
                    $session->disconnected_at = Carbon::now();
                    $session->save();

                    Log::info('清理无效WebSocket会话', ['session_id' => $sessionId]);
                }
                return;
            }

            // 推送消息到WebSocket连接
            $success = $server->push($connectionId, json_encode($message));

            if ($success) {
                Log::info('WebSocket消息推送成功', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId,
                    'event' => $message['event'] ?? 'unknown'
                ]);
            } else {
                Log::warning('WebSocket消息推送失败', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId
                ]);
            }

        } catch (\Exception $e) {
            Log::error('推送WebSocket消息异常', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 清理无效的会话状态
     * 检查数据库中状态为connected但实际连接已断开的会话
     */
    private function cleanupInvalidSessions($server): void
    {
        try {
            $this->info("🧹 开始清理无效会话状态...");

            // 获取所有状态为connected和pending的会话
            $activeSessions = \App\Models\WebSocketSession::whereIn('status', ['connected', 'pending'])->get();

            $cleanedCount = 0;
            $totalChecked = $activeSessions->count();

            foreach ($activeSessions as $session) {
                $isConnectionValid = false;
                $cachedFd = null; // 初始化变量

                // 🔧 新增：检查pending状态的会话是否超时
                if ($session->status === 'pending') {
                    // pending状态超过10分钟未连接，视为超时
                    $createdAt = Carbon::parse($session->created_at);
                    $timeoutMinutes = 10;

                    if ($createdAt->addMinutes($timeoutMinutes)->isPast()) {
                        // pending状态超时，直接标记为无效
                        $isConnectionValid = false;

                        Log::info('pending状态会话超时', [
                            'session_id' => $session->session_id,
                            'user_id' => $session->user_id,
                            'created_at' => $session->created_at,
                            'timeout_minutes' => $timeoutMinutes,
                            'reason' => 'pending状态超过10分钟未连接'
                        ]);
                    } else {
                        // pending状态未超时，跳过检查
                        continue;
                    }
                } else {
                    // connected状态的会话，检查实际连接和心跳时间
                    $cachedFd = Cache::get("websocket_session_{$session->session_id}");

                    if ($cachedFd) {
                        // 检查Swoole服务器中是否还存在这个连接
                        if ($server->exist($cachedFd)) {
                            // 🚀 新增：检查心跳时间
                            $isConnectionValid = $this->checkHeartbeatTimeout($session, $cachedFd);

                            if (!$isConnectionValid) {
                                Log::info('WebSocket会话心跳超时', [
                                    'session_id' => $session->session_id,
                                    'user_id' => $session->user_id,
                                    'fd' => $cachedFd,
                                    'last_ping_at' => $session->last_ping_at?->format('Y-m-d H:i:s'),
                                    'reason' => '心跳超时，超过5分钟无心跳'
                                ]);
                            }
                        } else {
                            // 连接不存在，清理缓存
                            Cache::forget("websocket_session_{$session->session_id}");
                            Cache::forget("websocket_fd_{$cachedFd}");

                            // 🔧 清理心跳缓存数据
                            $heartbeatCacheKey = "websocket_heartbeat_{$cachedFd}";
                            Cache::forget($heartbeatCacheKey);
                        }
                    }
                }

                // 如果连接无效，更新数据库状态
                if (!$isConnectionValid) {
                    $disconnectReason = '';

                    if ($session->status === 'pending') {
                        // pending状态的会话长时间未连接
                        $disconnectReason = 'pending状态超时，认证后未及时建立连接';
                    } else {
                        // connected状态的会话连接已断开或心跳超时
                        if ($cachedFd && $server->exist($cachedFd)) {
                            $disconnectReason = '心跳超时，超过5分钟无心跳信号';
                        } else {
                            $disconnectReason = '连接已断开，定时清理检测到无效连接';
                        }
                    }

                    $session->status = 'disconnected';
                    $session->disconnect_reason = $disconnectReason;
                    $session->disconnected_at = Carbon::now();
                    $session->save();

                    $cleanedCount++;

                    Log::info('清理无效WebSocket会话', [
                        'session_id' => $session->session_id,
                        'user_id' => $session->user_id,
                        'business_type' => $session->business_type,
                        'client_type' => $session->client_type,
                        'original_status' => $session->status,
                        'cached_fd' => $cachedFd,
                        'last_ping_at' => $session->last_ping_at?->format('Y-m-d H:i:s'),
                        'last_ping_minutes_ago' => $session->last_ping_at ? $session->last_ping_at->diffInMinutes(Carbon::now()) : null,
                        'reason' => $disconnectReason
                    ]);
                }
            }

            // 🚀 新增：诊断数据不一致问题
            $this->diagnoseCacheDbInconsistency($server, $totalChecked, $cleanedCount);

            $this->info("🧹 会话状态清理完成: 检查了{$totalChecked}个活跃会话(connected+pending)，清理了{$cleanedCount}个无效会话");

            if ($cleanedCount > 0) {
                Log::info('WebSocket会话状态清理完成', [
                    'total_checked' => $totalChecked,
                    'cleaned_count' => $cleanedCount,
                    'checked_statuses' => ['connected', 'pending'],
                    'timestamp' => Carbon::now()->format('c')
                ]);
            }

        } catch (\Exception $e) {
            $this->error("❌ 清理无效会话状态失败: " . $e->getMessage());
            Log::error('WebSocket会话状态清理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 诊断缓存和数据库数据不一致问题
     */
    private function diagnoseCacheDbInconsistency($server, int $dbActiveCount, int $cleanedCount): void
    {
        try {
            // 1. 统计实际的Swoole连接数
            $actualConnections = 0;
            $cacheConnections = 0;
            $validCacheConnections = 0;

            foreach ($server->connections as $fd) {
                if ($server->exist($fd) && $server->isEstablished($fd)) {
                    $actualConnections++;

                    // 检查是否有对应的缓存
                    $sessionId = Cache::get("websocket_fd_{$fd}");
                    if ($sessionId) {
                        $cacheConnections++;

                        // 检查反向缓存是否一致
                        $cachedFd = Cache::get("websocket_session_{$sessionId}");
                        if ($cachedFd == $fd) {
                            $validCacheConnections++;
                        }
                    }
                }
            }

            // 2. 统计数据库中的会话状态
            $dbStats = \App\Models\WebSocketSession::selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            // 3. 统计缓存中的映射数量
            $cacheStats = $this->getCacheStats();

            // 4. 输出诊断信息
            $this->info("📊 数据一致性诊断:");
            $this->info("   实际Swoole连接数: {$actualConnections}");
            $this->info("   有缓存映射的连接: {$cacheConnections}");
            $this->info("   缓存映射一致的连接: {$validCacheConnections}");
            $this->info("   数据库活跃会话数: {$dbActiveCount}");
            $this->info("   本次清理的会话数: {$cleanedCount}");

            $this->info("📈 数据库会话状态分布:");
            foreach ($dbStats as $status => $count) {
                $this->info("   {$status}: {$count}");
            }

            // 5. 如果数据不一致，记录详细日志
            if ($actualConnections != $dbActiveCount || $cacheConnections != $validCacheConnections) {
                Log::warning('WebSocket数据不一致检测', [
                    'actual_connections' => $actualConnections,
                    'cache_connections' => $cacheConnections,
                    'valid_cache_connections' => $validCacheConnections,
                    'db_active_sessions' => $dbActiveCount,
                    'cleaned_sessions' => $cleanedCount,
                    'db_status_distribution' => $dbStats,
                    'cache_stats' => $cacheStats,
                    'inconsistency_detected' => true
                ]);
            }

        } catch (\Exception $e) {
            $this->error("❌ 数据一致性诊断失败: " . $e->getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     */
    private function getCacheStats(): array
    {
        try {
            // 这里可以根据实际的缓存实现来统计
            // 由于Laravel Cache没有直接的方法来统计特定前缀的键数量
            // 我们返回一个简化的统计
            return [
                'note' => '缓存统计需要根据具体缓存实现来完善'
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}