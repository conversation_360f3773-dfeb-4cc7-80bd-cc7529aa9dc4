<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\CharacterLibrary;
use App\Models\UserCharacterBinding;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use Carbon\Carbon;
use App\Services\Common\TransactionManager;

/**
 * 角色管理服务
 */
class CharacterService
{

    
    /**
     * 获取角色分类列表 
     */
    public function getCategories(): array
    {
        try {
            $categories = [
                ['name' => CharacterLibrary::CATEGORY_HUMAN, 'value' => CharacterLibrary::CATEGORY_HUMAN],
                ['name' => CharacterLibrary::CATEGORY_ANTHROPOMORPHIC, 'value' => CharacterLibrary::CATEGORY_ANTHROPOMORPHIC],
                ['name' => CharacterLibrary::CATEGORY_ANIMAL, 'value' => CharacterLibrary::CATEGORY_ANIMAL],
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $categories
            ];

        } catch (\Exception $e) {
            Log::error('获取角色分类失败', [
                'method' => __METHOD__,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取角色分类失败',
                'data' => null
            ];
        }
    }

    /**
     * 绑定角色 (扩展版本 - 支持完整绑定参数)
     */
    public function bindCharacter(int $userId, int $characterId, array $bindingParams = []): array
    {
        try {
            DB::beginTransaction();

            // 解析绑定参数
            $reason = $bindingParams['reason'] ?? '';
            $storyboardPositionId = $bindingParams['storyboard_position_id'] ?? null;
            $bindingContext = $bindingParams['binding_context'] ?? 'library';
            $autoBind = $bindingParams['auto_bind'] ?? false;
            $compatibilityCheck = $bindingParams['compatibility_check'] ?? true;
            $bindingName = $bindingParams['binding_name'] ?? null;
            $customConfig = $bindingParams['custom_config'] ?? [];

            // 检查角色是否存在
            $character = CharacterLibrary::active()->find($characterId);
            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '角色不存在',
                    'data' => []
                ];
            }

            // 兼容性检查
            if ($compatibilityCheck && $storyboardPositionId) {
                $compatibilityResult = $this->checkCharacterCompatibility($character, $storyboardPositionId);
                if (!$compatibilityResult['compatible']) {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => '角色与分镜位置不兼容：' . $compatibilityResult['reason'],
                        'data' => []
                    ];
                }
            }

            // 检查是否已经绑定
            $existingBinding = UserCharacterBinding::byUser($userId)
                ->byCharacter($characterId)
                ->first();

            if ($existingBinding) {
                if ($existingBinding->is_active) {
                    return [
                        'code' => ApiCodeEnum::FAIL,
                        'message' => '角色已经绑定',
                        'data' => []
                    ];
                } else {
                    // 重新激活绑定
                    $existingBinding->is_active = true;
                    $existingBinding->binding_name = $bindingName ?: $character->name;
                    $existingBinding->custom_config = $customConfig;
                    $existingBinding->storyboard_position_id = $storyboardPositionId;
                    $existingBinding->binding_context = $bindingContext;
                    $existingBinding->save();

                    $character->incrementBinding();
                    DB::commit();

                    return [
                        'code' => ApiCodeEnum::SUCCESS,
                        'message' => '角色重新绑定成功',
                        'data' => [
                            'binding_id' => $existingBinding->id,
                            'character_id' => $character->id,
                            'character_name' => $character->name,
                            'binding_name' => $existingBinding->getDisplayName(),
                            'created_at' => $existingBinding->created_at->format('Y-m-d H:i:s')
                        ]
                    ];
                }
            }

            // 创建新绑定 (扩展版本)
            $binding = UserCharacterBinding::create([
                'user_id' => $userId,
                'character_id' => $characterId,
                'binding_name' => $bindingName ?: $character->name, // 默认使用角色名称
                'binding_reason' => $reason, // 绑定原因
                'custom_config' => $customConfig,
                'storyboard_position_id' => $storyboardPositionId, // 分镜位置ID
                'binding_context' => $bindingContext, // 绑定上下文
                'is_active' => true
            ]);

            // 增加角色绑定次数
            $character->incrementBinding();

            DB::commit();

            Log::info('角色绑定成功', [
                'user_id' => $userId,
                'character_id' => $characterId,
                'binding_id' => $binding->id
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色绑定成功',
                'data' => [
                    'binding_id' => $binding->id,
                    'character_id' => $character->id,
                    'character_name' => $character->name,
                    'binding_name' => $binding->getDisplayName(),
                    'storyboard_position_id' => $storyboardPositionId,
                    'binding_context' => $bindingContext,
                    'created_at' => $binding->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'character_id' => $characterId,
                'reason' => $reason,
                'binding_name' => $bindingName,
            ];

            Log::error('角色绑定失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色绑定失败',
                'data' => null
            ];
        }
    }

    /**
     * 检查角色与分镜位置的兼容性
     */
    private function checkCharacterCompatibility(CharacterLibrary $character, string $storyboardPositionId): array
    {
        try {
            // 这里应该实现真实的兼容性检查逻辑
            // 目前返回兼容，实际项目中需要根据分镜位置的要求检查角色属性

            // 示例兼容性检查规则：
            // 1. 检查角色风格是否匹配分镜要求
            // 2. 检查角色年龄范围是否适合场景
            // 3. 检查角色性格是否符合剧情需要

            Log::info('角色兼容性检查', [
                'character_id' => $character->id,
                'character_name' => $character->name,
                'storyboard_position_id' => $storyboardPositionId
            ]);

            return [
                'compatible' => true,
                'reason' => '',
                'suggestions' => []
            ];
        } catch (\Exception $e) {
            Log::error('角色兼容性检查失败', [
                'character_id' => $character->id,
                'storyboard_position_id' => $storyboardPositionId,
                'error' => $e->getMessage()
            ]);

            return [
                'compatible' => false,
                'reason' => '兼容性检查失败',
                'suggestions' => []
            ];
        }
    }

    /**
     * 解绑角色
     */
    public function unbindCharacter(int $bindingId, int $userId): array
    {
        try {
            DB::beginTransaction();

            $binding = UserCharacterBinding::byUser($userId)->find($bindingId);
            if (!$binding) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '绑定不存在',
                    'data' => []
                ];
            }

            if (!$binding->is_active) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '绑定已解除',
                    'data' => []
                ];
            }

            // 解除绑定
            $binding->is_active = false;
            $binding->save();

            // 减少角色绑定次数
            $binding->character->decrementBinding();

            DB::commit();

            Log::info('角色解绑成功', [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'character_id' => $binding->character_id
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色解绑成功',
                'data' => [
                    'binding_id' => $bindingId,
                    'unbind_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'binding_id' => $bindingId,
            ];

            Log::error('角色解绑失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色解绑失败',
                'data' => null
            ];
        }
    }

    /**
     * 基于文件创建角色
     */
    public function createCharacterFromFile(int $userId, ?int $projectId = null, array $creationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 解析创建参数
            $fileId = $creationParams['file_id'];
            $style = $creationParams['style'] ?? null;
            $publishToLibrary = $creationParams['publish_to_library'] ?? false;
            $autoBind = $creationParams['auto_bind'] ?? false;
            $storyboardPositionId = $creationParams['storyboard_position_id'] ?? null;

            // 获取文件信息
            $file = \App\Models\UserFile::find($fileId);
            if (!$file) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '文件不存在',
                    'data' => []
                ];
            }

            // 验证文件所有权
            if ($file->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权访问该文件',
                    'data' => []
                ];
            }

            // 创建角色记录
            $character = CharacterLibrary::create([
                'name' => '自定义角色_' . date('YmdHis'),
                'thumbnail' => $file->file_url,
                'images' => [$file->file_url],
                'style' => $style,
                'created_by' => $userId,
                'project_id' => $projectId,
                'status' => 'published',
                'is_premium' => false,
                'is_featured' => false,
                'source_file_id' => $fileId
            ]);

            // 如果需要发布到角色库
            if ($publishToLibrary) {
                $character->is_public = true;
                $character->save();
            }

            // 如果需要自动绑定
            if ($autoBind) {
                $bindingParams = [
                    'reason' => '文件创建自动绑定',
                    'storyboard_position_id' => $storyboardPositionId,
                    'binding_context' => 'project',
                    'auto_bind' => true
                ];

                $bindingResult = $this->bindCharacter($userId, $character->id, $bindingParams);
                if ($bindingResult['code'] !== ApiCodeEnum::SUCCESS) {
                    Log::warning('自动绑定失败', [
                        'character_id' => $character->id,
                        'user_id' => $userId,
                        'error' => $bindingResult['message']
                    ]);
                }
            }

            DB::commit();

            Log::info('基于文件创建角色成功', [
                'character_id' => $character->id,
                'user_id' => $userId,
                'file_id' => $fileId,
                'style' => $style
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色创建成功',
                'data' => [
                    'character_id' => $character->id,
                    'name' => $character->name,
                    'image_url' => $character->thumbnail,
                    'style' => $character->style,
                    'is_public' => $character->is_public ?? false,
                    'created_at' => $character->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'project_id' => $projectId,
                'creation_params' => $creationParams,
            ];

            Log::error('基于文件创建角色失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 更新绑定
     */
    public function updateBinding(int $bindingId, int $userId, array $updateData): array
    {
        try {
            $binding = UserCharacterBinding::byUser($userId)->find($bindingId);
            if (!$binding) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '绑定不存在',
                    'data' => []
                ];
            }

            $binding->fill($updateData);
            $binding->save();

            Log::info('绑定更新成功', [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'update_data' => array_keys($updateData)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '绑定更新成功',
                'data' => [
                    'binding_id' => $binding->id,
                    'binding_name' => $binding->getDisplayName(),
                    'is_favorite' => $binding->is_favorite,
                    'updated_at' => $binding->updated_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'update_data_keys' => array_keys($updateData),
            ];

            Log::error('绑定更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '绑定更新失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户绑定列表
     */
    public function getUserBindings(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = UserCharacterBinding::with(['character'])
                ->byUser($userId)
                ->active();

            // 应用筛选条件
            if (isset($filters['is_favorite']) && $filters['is_favorite']) {
                $query->favorite();
            }

            if (!empty($filters['category'])) {
                $query->whereHas('character', function ($q) use ($filters) {
                    $q->where('category', $filters['category']);
                });
            }

            // 排序
            switch ($filters['sort'] ?? 'usage') {
                case 'rating':
                    $query->byRating();
                    break;
                case 'created':
                    $query->orderBy('created_at', 'desc');
                    break;
                default:
                    $query->byUsage();
                    break;
            }

            $bindings = $query->paginate($perPage, ['*'], 'page', $page);

            $bindingsData = $bindings->map(function ($binding) {
                return [
                    'id' => $binding->id,
                    'character' => [
                        'id' => $binding->character->id,
                        'name' => $binding->character->name,
                        'thumbnail' => $binding->character->thumbnail,
                        'category' => $binding->character->category,
                        'rating' => $binding->character->rating
                    ],
                    'binding_name' => $binding->getDisplayName(),
                    'is_favorite' => $binding->is_favorite,
                    'usage_count' => $binding->usage_count,
                    'last_used_at' => $binding->last_used_at?->format('Y-m-d H:i:s'),
                    'user_rating' => $binding->user_rating,
                    'usage_frequency' => $binding->getUsageFrequency(),
                    'created_at' => $binding->created_at->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'bindings' => $bindingsData,
                    'pagination' => [
                        'current_page' => $bindings->currentPage(),
                        'total' => $bindings->total(),
                        'per_page' => $bindings->perPage(),
                        'last_page' => $bindings->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
            ];

            Log::error('获取用户绑定列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取用户绑定列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取角色列表
     */
    public function getCharacters(array $filters, int $page, int $perPage): array
    {
        try {
            $query = CharacterLibrary::active();

            // 应用筛选条件
            // tab参数处理：public/private
            if (!empty($filters['tab']) && $filters['tab'] === 'private') {
                // 私人角色筛选逻辑（基于created_by字段）
                $query->whereNotNull('created_by');
            } else {
                // 公开角色（系统预设角色，created_by为null）
                $query->whereNull('created_by');
            }

            if (!empty($filters['gender'])) {
                $query->byGender($filters['gender']);
            }

            if (!empty($filters['age_range'])) {
                $query->where('age_range', $filters['age_range']);
            }

            if (!empty($filters['search'])) {
                $query->search($filters['search']);
            }

            if (isset($filters['is_premium'])) {
                if ($filters['is_premium']) {
                    $query->premium();
                } else {
                    $query->free();
                }
            }

            if (isset($filters['is_featured']) && $filters['is_featured']) {
                $query->featured();
            }

            if (!empty($filters['tags'])) {
                foreach ($filters['tags'] as $tag) {
                    $query->byTag($tag);
                }
            }

            $characters = $query->ordered()->paginate($perPage, ['*'], 'page', $page);

            $charactersData = $characters->map(function ($character) {
                return [
                    'id' => $character->id,
                    'name' => $character->name,
                    'category' => $character->category,
                    'gender' => $character->gender,
                    'age_range' => $character->age_range,
                    'thumbnail' => $character->thumbnail,
                    'tags' => $character->tags ?? [],
                    'is_premium' => $character->is_premium,
                    'is_featured' => $character->is_featured,
                    'rating' => $character->rating,
                    'rating_count' => $character->rating_count,
                    'binding_count' => $character->binding_count
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'characters' => $charactersData,
                    'pagination' => [
                        'current_page' => $characters->currentPage(),
                        'total' => $characters->total(),
                        'per_page' => $characters->perPage(),
                        'last_page' => $characters->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
            ];

            Log::error('获取角色列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取角色列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取角色详情 
     */
    public function getCharacterDetail(int $characterId, int $userId): array
    {
        try {
            $character = CharacterLibrary::active()->find($characterId);

            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '角色不存在',
                    'data' => []
                ];
            }

            // 检查用户是否已绑定该角色
            $binding = UserCharacterBinding::byUser($userId)
                ->byCharacter($characterId)
                ->active()
                ->first();

            $characterData = [
                'id' => $character->id,
                'name' => $character->name,
                'category' => $character->category,
                'gender' => $character->gender,
                'age_range' => $character->age_range,
                'ai_prompt' => $character->ai_prompt,
                'thumbnail' => $character->thumbnail,
                'images' => $character->images ?? [],
                'tags' => $character->tags ?? [],
                'is_premium' => $character->is_premium,
                'is_featured' => $character->is_featured,
                'rating' => $character->rating,
                'rating_count' => $character->rating_count,
                'binding_count' => $character->binding_count,
                'is_bound' => !is_null($binding),
                'binding_id' => $binding?->id,
                'created_at' => $character->created_at->format('Y-m-d H:i:s')
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $characterData
            ];

        } catch (\Exception $e) {
            $error_context = [
                'character_id' => $characterId,
                'user_id' => $userId,
            ];

            Log::error('获取角色详情失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取角色详情失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取推荐角色
     */
    public function getRecommendations(int $userId, int $limit, string $type): array
    {
        try {
            $query = CharacterLibrary::active();

            switch ($type) {
                case 'popular':
                    $characters = $query->popular($limit)->get();
                    break;

                case 'similar':
                    $characters = $this->getSimilarCharacters($userId, $limit);
                    break;

                case 'new':
                    $characters = $query->orderBy('created_at', 'desc')->limit($limit)->get();
                    break;

                default:
                    $characters = $query->featured()->limit($limit)->get();
                    break;
            }

            $recommendationsData = $characters->map(function ($character) use ($type) {
                return [
                    'id' => $character->id,
                    'name' => $character->name,
                    'category' => $character->category,
                    'thumbnail' => $character->thumbnail,
                    'rating' => $character->rating,
                    'binding_count' => $character->binding_count,
                    'reason' => $this->getRecommendationReason($type)
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'recommendations' => $recommendationsData,
                    'recommendation_type' => $type,
                    'total' => $recommendationsData->count()
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'limit' => $limit,
                'type' => $type,
            ];

            Log::error('获取推荐角色失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取推荐角色失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取相似角色
     */
    private function getSimilarCharacters(int $userId, int $limit)
    {
        // 获取用户已绑定的角色
        $userBindings = UserCharacterBinding::byUser($userId)
            ->active()
            ->with('character')
            ->get();

        if ($userBindings->isEmpty()) {
            // 如果用户没有绑定角色，返回推荐角色
            return CharacterLibrary::active()->featured()->limit($limit)->get();
        }

        // 获取用户偏好的分类
        $preferredCategories = $userBindings->pluck('character.category')->unique();

        // 基于偏好推荐相似角色
        $query = CharacterLibrary::active()
            ->whereNotIn('id', $userBindings->pluck('character_id'));

        if ($preferredCategories->isNotEmpty()) {
            $query->whereIn('category', $preferredCategories);
        }

        return $query->limit($limit)->get();
    }

    /**
     * 获取推荐理由
     */
    private function getRecommendationReason(string $type): string
    {
        $reasons = [
            'popular' => '热门推荐',
            'similar' => '基于您的偏好推荐',
            'new' => '最新角色',
            'featured' => '精选推荐'
        ];

        return $reasons[$type] ?? '系统推荐';
    }







    /**
     * 更新角色数据（基于解析后的角色数据数组）
     * 使用事务管理确保数据一致性
     *
     * @param int $characterId 角色ID
     * @param array $characterData 解析后的角色数据数组
     * @param int $userId 用户ID（用于日志记录）
     * @return array 处理结果
     */
    public function updateCharacterFromData(int $characterId, array $characterData, int $userId): array
    {
        try {
            // 开始事务
            TransactionManager::begin('CharacterService.updateCharacterFromData');

            // 验证角色是否存在
            $character = CharacterLibrary::find($characterId);
            if (!$character) {
                TransactionManager::rollback('CharacterService.updateCharacterFromData', '指定的角色不存在');
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '指定的角色不存在',
                    'data' => []
                ];
            }

            // 如果没有需要更新的数据，直接返回成功
            if (empty($characterData)) {
                TransactionManager::commit('CharacterService.updateCharacterFromData');

                Log::info('角色数据无需更新', [
                    'user_id' => $userId,
                    'character_id' => $characterId,
                    'character_name' => $character->name,
                    'character_data' => $characterData
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '角色数据无需更新',
                    'data' => [
                        'character_id' => $characterId,
                        'character_name' => $character->name,
                        'updated_data' => [],
                        'parsed_character_data' => $characterData
                    ]
                ];
            }

            // 更新角色数据
            $character->update($characterData);

            // 提交事务
            TransactionManager::commit('CharacterService.updateCharacterFromData');

            Log::info('角色数据更新成功', [
                'user_id' => $userId,
                'character_id' => $characterId,
                'character_name' => $character->name,
                'updated_fields' => array_keys($characterData),
                'parsed_data' => $characterData
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色数据更新成功',
                'data' => [
                    'character_id' => $characterId,
                    'character_name' => $character->name,
                    'updated_data' => $characterData,
                    'parsed_character_data' => $characterData
                ]
            ];

        } catch (\Exception $e) {
            // 回滚事务
            TransactionManager::rollback('CharacterService.updateCharacterFromData', $e->getMessage());

            Log::error('更新角色数据失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'character_id' => $characterId,
                'character_data' => $characterData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '更新角色数据失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }



    /**
     * 验证角色是否存在且激活
     *
     * @param int $characterId 角色ID
     * @return array 验证结果
     */
    public function validateCharacterExists(int $characterId): array
    {
        try {
            $character = CharacterLibrary::where('id', $characterId)
                ->where('status', 'draft')
                ->first();

            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '指定的角色不存在或已被禁用',
                    'data' => []
                ];
            }

            Log::info('角色验证成功', [
                'character_id' => $characterId,
                'character_name' => $character->name
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色验证成功',
                'data' => [
                    'character' => $character
                ]
            ];

        } catch (\Exception $e) {
            Log::error('角色验证失败', [
                'method' => __METHOD__,
                'character_id' => $characterId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色验证失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }
}
