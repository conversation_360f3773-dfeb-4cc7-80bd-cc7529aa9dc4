<?php

use Illuminate\Support\Facades\Route;
use App\Services\AiServiceClient;
use Illuminate\Http\Request;

/**
 * 测试 AiServiceClient 新功能的路由
 */

// 获取平台选项列表
Route::get('/test/ai/platform-options/{taskType}', function (Request $request, string $taskType) {
    $userId = $request->query('user_id');
    
    $result = AiServiceClient::getPlatformOptions($taskType, $userId);
    
    return response()->json($result);
});

// 验证平台选择
Route::post('/test/ai/validate-platform', function (Request $request) {
    $platform = $request->input('platform');
    $taskType = $request->input('task_type');
    
    $result = AiServiceClient::validatePlatformChoice($platform, $taskType);
    
    return response()->json($result);
});

// 带用户选择的AI调用
Route::post('/test/ai/call-with-choice', function (Request $request) {
    $platform = $request->input('platform');
    $taskType = $request->input('task_type');
    $userId = $request->input('user_id');
    $data = $request->input('data', []);
    $options = $request->input('options', []);
    
    $result = AiServiceClient::callWithUserChoice($platform, $taskType, $data, $userId, $options);
    
    return response()->json($result);
});

// 获取用户推荐
Route::get('/test/ai/user-recommendations/{userId}/{taskType}', function (Request $request, int $userId, string $taskType) {
    $limit = $request->query('limit', 3);
    
    $result = AiServiceClient::getUserRecommendations($userId, $taskType, $limit);
    
    return response()->json($result);
});

// 记录用户偏好（手动测试）
Route::post('/test/ai/record-preference', function (Request $request) {
    $userId = $request->input('user_id');
    $platform = $request->input('platform');
    $taskType = $request->input('task_type');
    $success = $request->input('success', true);
    $executionTime = $request->input('execution_time', 1.0);
    
    AiServiceClient::recordUserPreference($userId, $platform, $taskType, $success, $executionTime);
    
    return response()->json([
        'success' => true,
        'message' => '用户偏好记录成功'
    ]);
});

// 获取支持的平台列表
Route::get('/test/ai/supported-platforms', function () {
    $platforms = AiServiceClient::getSupportedPlatforms();
    
    return response()->json([
        'success' => true,
        'data' => $platforms
    ]);
});
