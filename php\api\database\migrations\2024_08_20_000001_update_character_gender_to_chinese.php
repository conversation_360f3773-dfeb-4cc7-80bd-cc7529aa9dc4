<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 将角色库性别字段从英文更新为中文
 * 
 * 更新内容：
 * - male → 男性
 * - female → 女性  
 * - other → 未知
 */
class UpdateCharacterGenderToChinese extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 更新现有数据的性别字段
        DB::table('character_library')->where('gender', 'male')->update(['gender' => '男性']);
        DB::table('character_library')->where('gender', 'female')->update(['gender' => '女性']);
        DB::table('character_library')->where('gender', 'other')->update(['gender' => '未知']);
        
        // 记录迁移日志
        Log::info('角色性别字段中文化迁移完成', [
            'migration' => 'UpdateCharacterGenderToChinese',
            'updated_mappings' => [
                'male' => '男性',
                'female' => '女性',
                'other' => '未知'
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 回滚操作：将中文性别改回英文
        DB::table('character_library')->where('gender', '男性')->update(['gender' => 'male']);
        DB::table('character_library')->where('gender', '女性')->update(['gender' => 'female']);
        DB::table('character_library')->where('gender', '未知')->update(['gender' => 'other']);

        // 记录回滚日志
        Log::info('角色性别字段中文化迁移回滚完成', [
            'migration' => 'UpdateCharacterGenderToChinese',
            'rollback_mappings' => [
                '男性' => 'male',
                '女性' => 'female',
                '未知' => 'other'
            ]
        ]);
    }
}
