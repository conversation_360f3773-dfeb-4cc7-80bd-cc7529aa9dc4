<?php

namespace Tests\Integration;

use Tests\TestCase;
use App\Models\User;
use App\Models\CharacterLibrary;
use App\Models\UserCharacterBinding;
use App\Models\UserFile;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Queue;

/**
 * 角色创建功能集成测试
 * 测试完整的业务流程
 */
class CharacterCreationIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $project;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户和项目
        $this->user = User::factory()->create([
            'points' => 1000 // 确保有足够积分
        ]);
        
        $this->project = Project::factory()->create([
            'user_id' => $this->user->id,
            'name' => '集成测试项目'
        ]);

        // 模拟存储
        Storage::fake('public');
        
        // 禁用队列，使用同步执行
        Queue::fake();
    }



    /**
     * 测试完整的文件上传创建角色流程
     */
    public function testCompleteFileUploadCharacterCreationFlow()
    {
        // 第一步：上传文件
        $uploadFile = UploadedFile::fake()->image('character.jpg', 800, 600);
        
        $uploadResponse = $this->actingAs($this->user)
            ->postJson('/py-api/files/upload', [
                'file' => $uploadFile,
                'folder' => 'characters',
                'is_public' => false,
                'is_temporary' => false
            ]);

        $uploadResponse->assertStatus(200);
        $fileId = $uploadResponse->json('data.file_id');

        // 第二步：基于文件创建角色
        $createResponse = $this->actingAs($this->user)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => $fileId,
                'style' => '动漫可爱3.0',
                'project_id' => $this->project->id,
                'publish_to_library' => true,
                'auto_bind' => true,
                'storyboard_position_id' => 'pos_file_upload_test'
            ]);

        $createResponse->assertStatus(200)
                      ->assertJsonStructure([
                          'code',
                          'message',
                          'data' => [
                              'character_id',
                              'name',
                              'image_url',
                              'style'
                          ]
                      ]);

        $characterId = $createResponse->json('data.character_id');

        // 验证角色创建和自动绑定
        $this->assertDatabaseHas('character_library', [
            'id' => $characterId,
            'style' => '动漫可爱3.0',
            'project_id' => $this->project->id,
            'is_public' => true,
            'source_file_id' => $fileId
        ]);

        $this->assertDatabaseHas('user_character_bindings', [
            'user_id' => $this->user->id,
            'character_id' => $characterId,
            'storyboard_position_id' => 'pos_file_upload_test',
            'binding_context' => 'project'
        ]);

        // 第三步：验证文件关联
        $this->assertDatabaseHas('user_files', [
            'id' => $fileId,
            'user_id' => $this->user->id,
            'is_temporary' => false
        ]);
    }

    /**
     * 测试角色创建到项目使用的完整流程
     */
    public function testCharacterCreationToProjectUsageFlow()
    {
        // 第一步：创建多个角色
        $characters = [];
        
        for ($i = 1; $i <= 3; $i++) {
            // 上传文件
            $uploadFile = UploadedFile::fake()->image("character_{$i}.jpg");
            $uploadResponse = $this->actingAs($this->user)
                ->postJson('/py-api/files/upload', [
                    'file' => $uploadFile,
                    'folder' => 'characters'
                ]);
            
            $fileId = $uploadResponse->json('data.file_id');

            // 创建角色
            $createResponse = $this->actingAs($this->user)
                ->postJson('/py-api/characters/create-from-file', [
                    'file_id' => $fileId,
                    'style' => "风格{$i}.0",
                    'project_id' => $this->project->id,
                    'auto_bind' => false
                ]);

            $characters[] = $createResponse->json('data.character_id');
        }

        // 第二步：将角色绑定到不同的分镜位置
        $storyboardPositions = ['pos_scene_1', 'pos_scene_2', 'pos_scene_3'];
        
        foreach ($characters as $index => $characterId) {
            $bindResponse = $this->actingAs($this->user)
                ->postJson('/py-api/characters/bind', [
                    'character_id' => $characterId,
                    'reason' => '场景' . ($index + 1) . '绑定',
                    'storyboard_position_id' => $storyboardPositions[$index],
                    'binding_context' => 'storyboard'
                ]);

            $bindResponse->assertStatus(200);
        }

        // 第三步：获取项目的所有角色绑定
        $bindingsResponse = $this->actingAs($this->user)
            ->getJson('/py-api/characters/my-bindings');

        $bindingsResponse->assertStatus(200)
                        ->assertJsonCount(3, 'data.bindings');

        // 验证每个绑定的数据完整性
        foreach ($characters as $index => $characterId) {
            $this->assertDatabaseHas('user_character_bindings', [
                'user_id' => $this->user->id,
                'character_id' => $characterId,
                'storyboard_position_id' => $storyboardPositions[$index],
                'binding_context' => 'storyboard',
                'is_active' => true
            ]);
        }
    }

    /**
     * 测试角色创建的错误处理流程
     */
    public function testCharacterCreationErrorHandling()
    {
        // 测试积分不足的情况（使用文件创建角色）
        $poorUser = User::factory()->create(['points' => 0]);

        $uploadFile = UploadedFile::fake()->image('test.jpg');
        $uploadResponse = $this->actingAs($poorUser)
            ->postJson('/py-api/files/upload', [
                'file' => $uploadFile,
                'folder' => 'characters'
            ]);

        $fileId = $uploadResponse->json('data.file_id');

        $response = $this->actingAs($poorUser)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => $fileId
            ]);

        $response->assertStatus(402); // 积分不足

        // 测试无效文件的情况
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => 'invalid_file_id'
            ]);

        $response->assertStatus(422); // 参数验证失败

        // 测试绑定不存在角色的情况
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/bind', [
                'character_id' => 99999
            ]);

        $response->assertStatus(404); // 角色不存在
    }

    /**
     * 测试并发角色创建
     */
    public function testConcurrentCharacterCreation()
    {
        $responses = [];
        
        // 模拟5个并发请求
        for ($i = 1; $i <= 5; $i++) {
            $uploadFile = UploadedFile::fake()->image("concurrent_{$i}.jpg");
            $uploadResponse = $this->actingAs($this->user)
                ->postJson('/py-api/files/upload', [
                    'file' => $uploadFile,
                    'folder' => 'characters'
                ]);
            
            $fileId = $uploadResponse->json('data.file_id');

            $response = $this->actingAs($this->user)
                ->postJson('/py-api/characters/create-from-file', [
                    'file_id' => $fileId,
                    'style' => "并发风格{$i}.0",
                    'project_id' => $this->project->id
                ]);

            $responses[] = $response;
        }

        // 验证所有请求都成功
        foreach ($responses as $response) {
            $response->assertStatus(200);
        }

        // 验证数据库中有5个角色记录
        $this->assertEquals(5, CharacterLibrary::where('project_id', $this->project->id)->count());
    }


}
