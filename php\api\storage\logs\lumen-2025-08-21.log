[2025-08-21 23:29:31] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-21 23:29:31] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 23:29:31] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_YRFnnMBPsfhHhMW2LNOaUHTMf108SFEI"} 
[2025-08-21 23:29:31] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-21 23:29:31] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-21 23:29:31] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-21 23:29:31] production.ERROR: WebSocket连接认证失败 {"method":"App\\Services\\PyApi\\WebSocketService::authenticateConnection","error_context":{"user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","client_version":null},"error":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'connected_at' cannot be null (Connection: mysql, SQL: insert into `p_websocket_sessions` (`client_type`, `status`, `message_count`, `session_id`, `user_id`, `business_type`, `client_version`, `connection_ip`, `user_agent`, `connection_info`, `connected_at`, `last_ping_at`, `updated_at`, `created_at`) values (python_tool, pending, 0, ws_YRFnnMBPsfhHhMW2LNOaUHTMf108SFEI, 14, text_generation_aistory, ?, 127.0.0.1, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, [], ?, 2025-08-21 23:29:31, 2025-08-21 23:29:31, 2025-08-21 23:29:31))","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, 'id')
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Builder.php(1968): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1333): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1298): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1137): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Builder.php(1023): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\WebSocketSession))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\WebSocketSession), Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php(377): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\WebSocketController.php(96): App\\Services\\PyApi\\WebSocketService->authenticateConnection(14, 'python_tool', 'aistory', '127.0.0.1', 'Mozilla/5.0 (Wi...', NULL, Array)
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\WebSocketController->authenticate(Object(Laravel\\Lumen\\Http\\Request))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\WebSocketController), 'authenticate', Array)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(27): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#36 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#37 {main}"} 
[2025-08-21 23:32:51] production.ERROR: Command "tinker" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"tinker\" is not defined. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('tinker')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-21 23:37:45] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-21 23:37:45] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 23:37:45] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_CtUtTtwfwQF0JuJbDQoAhsgc43rFFPrU"} 
[2025-08-21 23:37:45] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-21 23:37:45] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-21 23:37:45] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-21 23:37:45] production.INFO: WebSocket连接认证成功 {"session_id":"ws_CtUtTtwfwQF0JuJbDQoAhsgc43rFFPrU","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_CtUtTtwfwQF0JuJbDQoAhsgc43rFFPrU","expires_at":"2025-08-22T00:07:45+08:00"} 
[2025-08-21 23:37:45] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 23:37:45] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 23:37:45] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 23:37:45] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 23:37:45] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 23:38:03] production.INFO: WebSocket会话正常连接 {"session_id":"ws_CtUtTtwfwQF0JuJbDQoAhsgc43rFFPrU","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-21 23:38:17] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 23:38:17] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 23:38:17] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_ICgHdP1aDyC6Tdwsivd5JYxiDOBlJUZZ"} 
[2025-08-21 23:38:17] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_CtUtTtwfwQF0JuJbDQoAhsgc43rFFPrU","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T23:38:03+08:00"}]} 
[2025-08-21 23:38:17] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-21 23:38:17] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-21 23:38:17] production.INFO: WebSocket连接认证成功 {"session_id":"ws_ICgHdP1aDyC6Tdwsivd5JYxiDOBlJUZZ","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_ICgHdP1aDyC6Tdwsivd5JYxiDOBlJUZZ","expires_at":"2025-08-22T00:08:17+08:00"} 
[2025-08-21 23:38:38] production.INFO: WebSocket会话正常连接 {"session_id":"ws_ICgHdP1aDyC6Tdwsivd5JYxiDOBlJUZZ","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-21 23:38:51] production.INFO: User offline {"user_id":14} 
[2025-08-21 23:39:14] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 23:39:14] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 23:39:14] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_JBo8MDyTdBDvor4UfOg6vSaM2GPEd9qf"} 
[2025-08-21 23:39:14] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_CtUtTtwfwQF0JuJbDQoAhsgc43rFFPrU","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T23:38:03+08:00"}]} 
[2025-08-21 23:39:14] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-21 23:39:14] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-21 23:39:14] production.INFO: WebSocket连接认证成功 {"session_id":"ws_JBo8MDyTdBDvor4UfOg6vSaM2GPEd9qf","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_JBo8MDyTdBDvor4UfOg6vSaM2GPEd9qf","expires_at":"2025-08-22T00:09:14+08:00"} 
[2025-08-21 23:39:30] production.INFO: WebSocket会话正常连接 {"session_id":"ws_JBo8MDyTdBDvor4UfOg6vSaM2GPEd9qf","user_id":14,"fd":3,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-21 23:39:46] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:39:46] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:39:46] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755790786.01775,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:39:46] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":122.4,"total_stack_cleared":true} 
[2025-08-21 23:39:46] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:39:46+08:00"} 
[2025-08-21 23:39:47] production.INFO: WebSocket发送活跃连接通知 {"session_id":"ws_CtUtTtwfwQF0JuJbDQoAhsgc43rFFPrU","fd":4,"message":"当前session_id已建立连接，处在活跃状态"} 
[2025-08-21 23:40:09] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 23:40:09] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 23:40:09] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_eytKZGrRaufHp9Fx3sNsd45sRUrK23Z2"} 
[2025-08-21 23:40:09] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_CtUtTtwfwQF0JuJbDQoAhsgc43rFFPrU","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T23:38:03+08:00"},{"session_id":"ws_JBo8MDyTdBDvor4UfOg6vSaM2GPEd9qf","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T23:39:30+08:00"}]} 
[2025-08-21 23:40:09] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-21 23:40:09] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":0,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-21 23:40:09] production.INFO: WebSocket连接认证成功 {"session_id":"ws_eytKZGrRaufHp9Fx3sNsd45sRUrK23Z2","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_eytKZGrRaufHp9Fx3sNsd45sRUrK23Z2","expires_at":"2025-08-22T00:10:09+08:00"} 
[2025-08-21 23:40:25] production.INFO: WebSocket会话正常连接 {"session_id":"ws_eytKZGrRaufHp9Fx3sNsd45sRUrK23Z2","user_id":14,"fd":5,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-21 23:40:52] production.INFO: User offline {"user_id":14} 
[2025-08-21 23:41:42] production.INFO: User offline {"user_id":14} 
[2025-08-21 23:41:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:41:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:41:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755790905.978263,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:41:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":9.82,"total_stack_cleared":true} 
[2025-08-21 23:41:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:41:45+08:00"} 
[2025-08-21 23:41:57] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 23:41:57] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 23:41:57] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_fWCY71u1yJDwPEL8cnK7WdvJQBJXYT1D"} 
[2025-08-21 23:41:57] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_JBo8MDyTdBDvor4UfOg6vSaM2GPEd9qf","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T23:39:30+08:00"}]} 
[2025-08-21 23:41:57] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-21 23:41:57] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-21 23:41:57] production.INFO: WebSocket连接认证成功 {"session_id":"ws_fWCY71u1yJDwPEL8cnK7WdvJQBJXYT1D","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_fWCY71u1yJDwPEL8cnK7WdvJQBJXYT1D","expires_at":"2025-08-22T00:11:57+08:00"} 
[2025-08-21 23:42:13] production.INFO: WebSocket会话正常连接 {"session_id":"ws_fWCY71u1yJDwPEL8cnK7WdvJQBJXYT1D","user_id":14,"fd":6,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-21 23:43:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:43:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:43:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755791025.991506,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:43:46] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":84.16,"total_stack_cleared":true} 
[2025-08-21 23:43:46] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-21T23:43:46+08:00"} 
[2025-08-21 23:45:02] production.INFO: User offline {"user_id":14} 
[2025-08-21 23:45:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:45:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:45:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755791145.977548,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:45:46] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":44.18,"total_stack_cleared":true} 
[2025-08-21 23:45:46] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:45:46+08:00"} 
[2025-08-21 23:47:46] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:47:46] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:47:46] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755791266.011649,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:47:46] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":47.63,"total_stack_cleared":true} 
[2025-08-21 23:47:46] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:47:46+08:00"} 
[2025-08-21 23:47:52] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 23:47:52] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 23:47:52] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_L9zzdshoWuGAbIPSwh0PjNiGz8i82zqr"} 
[2025-08-21 23:47:52] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_fWCY71u1yJDwPEL8cnK7WdvJQBJXYT1D","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T23:42:13+08:00"}]} 
[2025-08-21 23:47:52] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-21 23:47:52] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-21 23:47:52] production.INFO: WebSocket连接认证成功 {"session_id":"ws_L9zzdshoWuGAbIPSwh0PjNiGz8i82zqr","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_L9zzdshoWuGAbIPSwh0PjNiGz8i82zqr","expires_at":"2025-08-22T00:17:52+08:00"} 
[2025-08-21 23:49:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:49:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:49:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755791385.964356,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:49:46] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":79.7,"total_stack_cleared":true} 
[2025-08-21 23:49:46] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:49:46+08:00"} 
[2025-08-21 23:51:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:51:46] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:51:46] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755791505.999905,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:51:46] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":62.51,"total_stack_cleared":true} 
[2025-08-21 23:51:46] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:51:46+08:00"} 
[2025-08-21 23:53:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:53:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:53:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755791625.963164,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:53:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":16.65,"total_stack_cleared":true} 
[2025-08-21 23:53:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:53:45+08:00"} 
[2025-08-21 23:55:46] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:55:46] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:55:46] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755791746.016122,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:55:46] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":102.35,"total_stack_cleared":true} 
[2025-08-21 23:55:46] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:55:46+08:00"} 
[2025-08-21 23:57:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:57:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:57:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755791865.95575,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:57:46] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":108.97,"total_stack_cleared":true} 
[2025-08-21 23:57:46] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:57:46+08:00"} 
[2025-08-21 23:59:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1170,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:59:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 23:59:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1170,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755791985.996207,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 23:59:46] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":101.83,"total_stack_cleared":true} 
[2025-08-21 23:59:46] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T23:59:46+08:00"} 
