<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('failed_jobs', function (Blueprint $table) {
            $table->id()->comment('主键ID，自增长');
            $table->uuid('uuid')->unique()->nullable(false)->default(Str::uuid())->comment('任务唯一标识符，用于去重和追踪');
            $table->text('connection')->comment('队列连接名称，标识使用的队列驱动');
            $table->text('queue')->comment('队列名称，标识任务所属的队列');
            $table->longText('payload')->comment('任务载荷数据，包含任务类名、参数等序列化信息');
            $table->longText('exception')->comment('异常信息，记录任务失败的详细错误信息和堆栈跟踪');
            $table->timestamp('failed_at')->useCurrent()->comment('任务失败时间，记录任务失败的具体时间戳');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('failed_jobs');
    }
};
