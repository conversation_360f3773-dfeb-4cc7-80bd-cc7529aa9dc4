<?php

namespace App\Jobs;

use App\Services\PyApi\AiGenerationService;
use App\Services\PyApi\WebSocketEventService;
use App\Enums\ApiCodeEnum;
use App\Helpers\EventTypeHelper;
use App\Exceptions\BusinessException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Services\Common\TransactionManager;

/**
 * 异步处理文本生成任务
 * 实现图表中的完整 WebSocket 进度推送流程
 */
class ProcessTextGeneration implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $userId;              // 用户ID（用于权限验证和日志记录）
    protected $prompt;              // 用户输入的提示词（AI生成的核心输入）
    protected $modelId;             // AI模型配置ID（指定使用哪个AI模型）
    protected $toolData;            // 工具数据（包含项目ID、分镜ID、风格ID等业务参数）
    protected $generationParams;    // AI生成参数（温度、最大令牌数、top_p等模型参数）
    protected $businessType;        // 业务类型（（php\api\config\ai.php 中 映射后的类型））
    protected $taskId;              // 外部任务ID（用于WebSocket通信和前端追踪）
    protected $taskRecordId;        // 任务记录ID（p_ai_generation_tasks表主键，用于状态更新）
    protected $context;             // 上下文（prompt_edit、manual_retry等，描述任务执行场景）

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 180; // 3分钟

    /**
     * 最大重试次数
     */
    public $tries = 3;

    /**
     * 获取任务的唯一标识符
     * 用于避免失败任务表的UUID冲突
     */
    public function uniqueId()
    {
        return 'text_gen_' . $this->taskId . '_' . $this->userId;
    }

    public function __construct(
        int $userId,                    // 用户ID
        string $prompt,                 // 用户输入的提示词
        int $modelId,                   // AI模型配置ID
        array $toolData = [],           // 工具数据（项目ID、分镜ID等）
        array $generationParams = [],   // AI生成参数
        string $businessType,           // 业务类型（php\api\config\ai.php 中 映射后的类型）
        string $taskId,                 // 外部任务ID（WebSocket通信用）
        ?int $taskRecordId = null,      // 任务记录ID（数据库主键）
        string $context = 'prompt_edit' // 上下文（执行场景描述）
    ) {
        $this->userId = $userId;
        $this->prompt = $prompt;
        $this->modelId = $modelId;
        $this->toolData = $toolData;
        $this->generationParams = $generationParams;
        $this->businessType = $businessType;
        $this->taskId = $taskId;
        $this->taskRecordId = $taskRecordId;
        $this->context = $context;
    }

    /**
     * 任务执行主方法
     *
     * 🔧 重要说明：这是 Laravel 队列系统的核心方法，由框架自动调用执行任务
     *
     * 📋 自动调用时机：
     * 1. dispatch() 创建任务后，队列工作进程自动调用
     * 2. 系统错误重试时，队列系统重新调用
     * 3. 手动重试任务时，队列系统调用
     *
     * 🔄 执行流程：
     * Laravel队列系统 → 获取任务 → 调用 handle() → 执行业务逻辑 → 成功/失败处理
     *
     * 🎯 主要职责：
     * 1. 获取WebSocket会话的业务类型
     * 2. 推送任务进度给前端用户
     * 3. 调用AI生成服务执行文本生成
     * 4. 处理生成结果（项目更新、分镜入库等）
     * 5. 推送任务完成事件并消费积分
     * 6. 异常处理：区分业务错误和系统错误
     *
     * 🔀 异常处理策略：
     * - 业务错误：推送失败事件 + 返还积分 + return（不重试）
     * - 系统错误：不推送事件 + throw异常（触发重试）
     *
     * 💡 与 failed() 方法的关系：
     * - handle()：执行业务逻辑，可能重试
     * - failed()：处理最终失败，在重试耗尽后自动调用
     *
     * @return void
     * @throws \Exception 系统错误时抛出异常触发重试
     */
    public function handle()
    {
        // 🔧 依赖注入：获取核心服务实例
        $webSocketEventService = app(WebSocketEventService::class); // WebSocket推送服务
        $aiGenerationService = app(AiGenerationService::class);     // AI生成服务

        try {
            // 🔄 步骤1：开启数据库事务
            // 确保整个任务执行过程的数据一致性，失败时可以完整回滚
            TransactionManager::begin('ProcessTextGeneration.handle');

            // 🔍 调试日志：记录事务开始状态
            Log::debug('ProcessTextGeneration.handle - 事务开始', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'transaction_level' => TransactionManager::getLevel(),
                'in_transaction' => TransactionManager::inTransaction(),
                'nesting_info' => TransactionManager::getNestingInfo(),
                'transaction_stack' => TransactionManager::getTransactionStack()
            ]);

            // 🎭 步骤3：推送任务进度给前端用户
            // 让用户实时了解任务执行状态，提升用户体验
            $this->pushProgress($webSocketEventService, 10, "开始文本生成任务");
            $this->pushProgress($webSocketEventService, 30, "连接AI平台");

            // 🤖 步骤4：调用AI生成服务执行文本生成
            // 这是核心业务逻辑，会更新已存在的任务记录并调用第三方AI平台
            $result = $aiGenerationService->generateText(
                $this->userId,          // 用户ID（权限验证）
                $this->prompt,          // 提示词（AI生成输入）
                $this->modelId,         // 模型配置ID（指定AI模型）
                $this->toolData,        // 工具数据（业务参数）
                $this->generationParams, // 生成参数（模型参数）
                $this->businessType,    // 业务类型（php\api\config\ai.php 中 映射后的类型）
                $this->taskId,          // 外部任务ID（WebSocket追踪）
                $this->taskRecordId     // 任务记录ID（数据库状态更新）
            );

            // 🎭 继续推送进度更新
            $this->pushProgress($webSocketEventService, 60, "AI文本生成完成");

            // 🔍 步骤5：记录任务参数（调试用）
            // 注意：积分预检查和冻结已在 generateTextWithWebSocket() 中完成
            Log::debug('ProcessTextGeneration Job - 任务参数', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'context' => $this->context,
                'estimated_cost' => $this->generationParams['estimated_cost'] ?? 'unknown',
                'points_status' => 'already_frozen_in_generateTextWithWebSocket'
            ]);

            // 📋 步骤6：处理AI生成结果
            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // ✅ 生成成功分支

                // 📊 推送后续处理进度
                $this->pushProgress($webSocketEventService, 80, "保存文本数据");
                $this->pushProgress($webSocketEventService, 100, "文本生成完成");

                // 🔔 步骤7：推送任务成功完成事件并消费积分
                // 这里会触发积分消费逻辑，将冻结的积分转为已消费状态
                /**
                * 🎭 前端处理统一机制：
                * - 监听 *_completed 事件 → 显示成功结果，隐藏进度条
                * - 监听 *_failed 事件 → 显示失败信息，隐藏进度条
                * - 积分处理：成功时消费积分，失败时返还积分
                */
                $pushResult = $webSocketEventService->pushAiGenerationCompleted(
                    $this->taskId,
                    $this->userId,
                    $this->businessType
                );

                // 📊 记录WebSocket推送结果
                Log::info('WebSocket推送结果 - 任务完成', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'push_success' => $pushResult['code'] === ApiCodeEnum::SUCCESS ? 'yes' : 'no',
                    'push_method' => 'Redis通道桥接',
                    'push_message' => $pushResult['message'] ?? '',
                    'points_consumed' => $pushResult['data']['points_handled'] ?? false // 积分是否已消费
                ]);

                // 🔍 调试日志：检查事务提交前状态
                Log::debug('ProcessTextGeneration.handle - 准备提交事务', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'transaction_level' => TransactionManager::getLevel(),
                    'in_transaction' => TransactionManager::inTransaction(),
                    'nesting_info' => TransactionManager::getNestingInfo(),
                    'transaction_stack' => TransactionManager::getTransactionStack()
                ]);

                // ✅ 步骤8：提交事务，确保所有数据变更生效
                TransactionManager::commit('ProcessTextGeneration.handle');

                // 🎉 任务成功完成日志
                Log::info('文本生成任务完成', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'context' => $this->context,
                    'ai_task_id' => $result['data']['task_id'] ?? null,
                    'cost' => $result['data']['cost'] ?? 0,
                    'processing_steps_completed' => [
                        '1_websocket_session_validation' => true,
                        '2_progress_notifications' => true,
                        '3_ai_text_generation' => true,
                        '4_parameter_validation' => true,
                        '5_result_processing' => true,
                        '6_websocket_completion_event' => $pushResult['code'] === ApiCodeEnum::SUCCESS,
                        '7_transaction_commit' => true
                    ]
                ]);

            } else {
                // ❌ 生成失败分支 - 抛出业务异常
                // 这里抛出的异常会被 catch 块捕获，根据异常类型决定是否重试
                throw new BusinessException(
                    $result['message'],
                    $result['code'] ?? 'AI_GENERATION_FAILED',
                    [
                        'task_id' => $this->taskId,
                        'user_id' => $this->userId,
                        'result' => $result
                    ]
                );
            }

        } catch (\Exception $e) {
            // ❌ 异常处理：这是任务执行的关键分叉点
            // 根据异常类型决定是立即失败还是重试，直接影响用户体验和积分处理

            // 🔍 步骤9：异常类型识别和分类
            $isBusinessException = BusinessException::isBusinessException($e);
            $errorType = $isBusinessException ? 'business_error' : 'system_error';

            // 记录回滚开始日志 - 根据异常类型调整日志详细程度
            $logData = [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'context' => $this->context,
                'error_message' => $e->getMessage(),
                'error_type' => $errorType,
                'is_business_exception' => $isBusinessException,
                'job_class' => self::class,
                'job_method' => 'handle',
                'rollback_reason' => '文本生成任务执行失败',
                'timestamp' => time()
            ];

            // 根据错误消息判断是否为业务错误，避免记录详细路径信息
            $isBusinessError = $this->isBusinessErrorByMessage($e->getMessage());

            // 只有非业务错误才记录详细的调试信息
            if (!$isBusinessError && !$isBusinessException) {
                $logData['exception_class'] = get_class($e);
                $logData['error_file'] = $e->getFile();
                $logData['error_line'] = $e->getLine();
            }

            Log::warning('ProcessTextGeneration - 开始事务回滚', $logData);

            // 🔧 修复时序问题：先推送失败事件，再执行事务回滚
            // 这样可以确保推送时任务记录还存在，避免"未找到任务记录"的错误

            // 🔧 在失败处理前推送进度，让用户知道处理进展
            $this->pushProgress($webSocketEventService, 90, "处理生成结果中...");

            Log::info('ProcessTextGeneration - 开始失败处理', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'error_message' => $e->getMessage(),
                'current_transaction_level' => TransactionManager::getLevel(),
                'business_type' => $this->businessType,
                'processing_order' => '1. 推送进度 -> 2. 推送失败事件 -> 3. 事务回滚 -> 4. 发布事件',
                'timestamp' => time()
            ]);

            // 🔀 步骤10：关键决策 - 业务错误 vs 系统错误处理策略
            /**
            * 🎯 积分处理的核心逻辑：
            * - 业务错误：立即推送失败事件 + 返还积分 + 不重试
            * - 系统错误：不推送事件 + 不处理积分 + 重试（重试耗尽后在failed()中处理）
            */
            if ($isBusinessException || $isBusinessError) {
                // 📢 业务错误分支：确定性失败，立即通知用户并返还积分
                $pushResult = $webSocketEventService->pushAiGenerationCompleted(
                    $this->taskId,
                    $this->userId,
                    $this->businessType, // 传入业务类型，避免数据库查询
                    $e->getMessage() // 传入错误消息表示失败
                );

                Log::info('WebSocket推送结果 - 业务错误失败', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'push_success' => $pushResult['code'] === ApiCodeEnum::SUCCESS ? 'yes' : 'no',
                    'push_method' => 'Redis通道桥接',
                    'error_message' => $e->getMessage(),
                    'error_type' => 'business_error',
                    'push_message' => $pushResult['message'] ?? '',
                    'points_refunded' => $pushResult['data']['points_handled'] ?? false, // 积分是否已返还
                    'will_retry' => false // 不会重试
                ]);
            } else {
                // 🔄 系统错误分支：可能恢复的错误，保留重试机会
                Log::info('系统错误，跳过WebSocket推送，等待重试', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'error_message' => $e->getMessage(),
                    'error_type' => 'system_error',
                    'will_retry' => true,
                    'max_tries' => $this->tries,
                    'points_status' => 'kept_frozen' // 积分保持冻结状态
                ]);
            }

            // 🔧 现在执行事务回滚（在WebSocket推送之后，事件发布之前）
            // 注意：事件发布需要在事务回滚之后，避免被回滚影响
            // 记录进入catch时的事务状态，用于诊断事务泄漏
            $currentLevel = TransactionManager::getLevel();
            $expectedLevel = 1; // 期望的级别应该是1（只有handle自己的事务）

            if ($currentLevel !== $expectedLevel) {
                Log::critical('ProcessTextGeneration - 检测到事务泄漏', [
                    'task_id' => $this->taskId,
                    'current_level' => $currentLevel,
                    'expected_level' => $expectedLevel,
                    'transaction_stack' => TransactionManager::getTransactionStack(),
                    'error_message' => $e->getMessage(),
                    'context' => 'transaction_leak_detected'
                ]);
            }

            // 执行正常的事务回滚（应该只需要一次）
            $rollbackResult = TransactionManager::rollback('ProcessTextGeneration.handle', $e->getMessage());

            // 记录回滚结果日志
            Log::warning('ProcessTextGeneration - 事务回滚完成', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'context' => $this->context,
                'rollback_success' => $rollbackResult ? 'yes' : 'no',
                'final_transaction_level' => TransactionManager::getLevel(),
                'rollback_reason' => $e->getMessage(),
                'transaction_context' => 'ProcessTextGeneration.handle',
                'job_class' => self::class,
                'job_method' => 'handle',
                'timestamp' => time(),
                'next_action' => 'publish_failure_event'
            ]);

            // 🔧 在事务回滚完成后发布失败事件，避免被回滚影响
            Log::info('ProcessTextGeneration - 开始发布失败事件', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'transaction_level' => TransactionManager::getLevel(),
                'business_type' => $this->businessType,
                'context' => 'post_rollback_event_publish'
            ]);

            // 📡 步骤11：发布失败事件到系统事件总线
            // 🎯 作用对象：
            // - 系统内部组件（统计服务、监控服务、通知服务等）
            // - 用于业务数据分析、错误统计、告警通知
            // - 发布的事件保存在：system_events 表中
            $this->publishFailureEvent($e->getMessage());

            // 根据异常类型调整日志详细程度
            $errorLogData = [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'context' => $this->context,
                'prompt' => substr($this->prompt, 0, 100),
                'error' => $e->getMessage(),
                'error_type' => $errorType
            ];

            // 只有非业务错误才记录完整的堆栈跟踪
            if (!$isBusinessError && !$isBusinessException) {
                $errorLogData['trace'] = $e->getTraceAsString();
                $errorLogData['exception_class'] = get_class($e);
                $errorLogData['error_file'] = $e->getFile();
                $errorLogData['error_line'] = $e->getLine();
            }

            Log::error('文本生成任务失败', $errorLogData);

            // 🎯 步骤12：最终决策 - 任务结束方式
            if ($isBusinessException || $isBusinessError) {
                // ✅ 业务错误：优雅结束，已完成所有必要处理
                Log::info('业务错误处理完成，任务正常结束', [
                    'task_id' => $this->taskId,
                    'error_type' => 'business_error',
                    'error_message' => $e->getMessage(),
                    'actions_completed' => [
                        'websocket_notification' => true,
                        'points_refunded' => true,
                        'transaction_rollback' => true,
                        'event_published' => true
                    ]
                ]);
                return; // 正常结束，不抛出异常，任务标记为完成
            } else {
                // 🔄 系统错误：抛出异常触发Laravel队列重试机制
                Log::error('系统错误，将进行重试', [
                    'task_id' => $this->taskId,
                    'error_type' => 'system_error',
                    'error_message' => $e->getMessage(),
                    'retry_strategy' => 'throw_exception_for_queue_retry',
                    'max_tries' => $this->tries,
                    'points_status' => 'remain_frozen_until_final_result'
                ]);
                throw $e; // 抛出异常，Laravel队列系统会自动重试
            }
        }
    }

    /**
     * 根据错误消息判断是否为业务错误
     */
    private function isBusinessErrorByMessage(string $message): bool
    {
        // 从配置文件读取业务错误关键词
        $businessErrorKeywords = config('ai.business_error_keywords', []);

        foreach ($businessErrorKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 任务最终失败处理方法
     *
     * 🔧 重要说明：这是 Laravel 队列系统的魔术方法，由框架自动调用，无需手动调用
     *
     * 📋 自动调用时机：
     * 1. handle() 方法抛出异常且重试次数达到上限（$tries = 3）
     * 2. 任务执行超时（$timeout = 180秒）
     * 3. 手动标记任务失败（通过 Artisan 命令等）
     *
     * 🔄 调用流程：
     * Laravel队列系统 → 检测失败条件 → 自动调用 failed() → 推送失败事件 → 返还积分
     *
     * 🎯 主要职责：
     * 1. 记录任务最终失败日志
     * 2. 清理残留事务（安全回滚）
     * 3. 推送WebSocket失败事件给前端
     * 4. 触发积分返还机制
     * 5. 发布失败事件到事件总线
     *
     * 💡 与 handle() 方法的区别：
     * - handle()：执行业务逻辑，可能重试
     * - failed()：处理最终失败，不会重试
     *
     * @param \Throwable $exception 导致任务失败的异常对象
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        $webSocketEventService = app(WebSocketEventService::class);

        // 🔍 步骤1：记录任务最终失败的详细日志
        // 这里记录的是经过所有重试后的最终失败，包含完整的异常信息
        Log::error('ProcessTextGeneration - 任务最终失败', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'context' => $this->context,
            'error_message' => $exception->getMessage(),
            'error_type' => get_class($exception),
            'error_file' => $exception->getFile(),
            'error_line' => $exception->getLine(),
            'job_class' => self::class,
            'job_method' => 'failed',
            'failure_reason' => '任务重试次数耗尽或致命错误',
            'max_tries' => $this->tries, // 最大重试次数
            'timeout_seconds' => $this->timeout, // 超时时间
            'timestamp' => time()
        ]);

        // 🔧 步骤2：安全回滚残留事务
        // 由于任务失败可能发生在事务中间，需要确保清理所有未提交的事务
        // 防止数据库连接泄漏和数据不一致问题
        if (TransactionManager::inTransaction()) {
            Log::warning('ProcessTextGeneration - 检测到残留事务，执行安全回滚', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'transaction_level' => TransactionManager::getLevel(),
                'context' => 'failed_method_safety_rollback',
                'reason' => '任务最终失败，清理残留事务',
                'timestamp' => time()
            ]);

            $safeRollbackResult = TransactionManager::safeRollback('ProcessTextGeneration.failed', $exception->getMessage());

            Log::warning('ProcessTextGeneration - 安全回滚完成', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'rollback_executed' => $safeRollbackResult ? 'yes' : 'no',
                'context' => 'failed_method_safety_rollback',
                'final_transaction_level' => TransactionManager::getLevel(),
                'timestamp' => time()
            ]);
        }

        // 🔔 步骤3：推送WebSocket失败事件给前端
        // 通知前端用户任务最终失败，隐藏进度条，显示失败信息
        // 同时触发积分返还机制（在 pushAiGenerationFailed 内部处理）
        Log::info('WebSocket推送事件 - 任务最终失败', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'error_message' => $exception->getMessage(),
            'push_type' => EventTypeHelper::getFailedEventType('text_generation') ?? 'ai_generation_failed',
            'context' => 'failed_method',
            'will_refund_points' => true // 将会返还积分
        ]);

        // 🎯 关键调用：推送失败事件并返还积分
        $failedPushResult = $webSocketEventService->pushAiGenerationFailed(
            $this->taskId,
            $this->userId,
            $exception->getMessage()
        );

        // 📊 记录WebSocket推送结果
        Log::info('WebSocket推送结果 - 任务最终失败', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'push_success' => $failedPushResult['code'] === ApiCodeEnum::SUCCESS ? 'yes' : 'no',
            'push_message' => $failedPushResult['message'] ?? '',
            'points_handled' => $failedPushResult['data']['points_handled'] ?? false, // 积分是否已处理
            'context' => 'failed_method_websocket_result'
        ]);

        // 📡 步骤4：发布失败事件到事件总线
        // 用于系统内部其他组件监听和处理（如统计、通知等）
        $this->publishFailureEvent($exception->getMessage());

        // 📝 最终失败总结日志
        Log::error('文本生成任务最终失败 - 处理完成', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'context' => $this->context,
            'error' => $exception->getMessage(),
            'job_class' => self::class,
            'job_method' => 'failed',
            'max_tries' => $this->tries,
            'timeout_seconds' => $this->timeout,
            'processing_steps_completed' => [
                '1_detailed_logging' => true,
                '2_transaction_cleanup' => true,
                '3_websocket_notification' => $failedPushResult['code'] === ApiCodeEnum::SUCCESS,
                '4_event_bus_publish' => true
            ],
            'timestamp' => time()
        ]);
    }

    /**
     * 发布失败事件到系统事件总线
     *
     * 🎯 核心作用：通知系统内部组件任务失败，用于统计、监控、告警等
     *
     * 📋 与 WebSocket 事件的区别：
     * ┌─────────────────┬──────────────────────┬─────────────────────────┐
     * │     事件类型     │   pushAiGenerationCompleted │   publishFailureEvent   │
     * ├─────────────────┼──────────────────────┼─────────────────────────┤
     * │   通知对象       │      前端用户界面      │     后端系统组件        │
     * │   传输方式       │      WebSocket       │      HTTP API          │
     * │   主要作用       │   用户体验+积分处理    │   系统监控+数据统计     │
     * │   执行时机       │   立即(业务错误时)    │      总是执行          │
     * │   受众范围       │      单个用户        │      整个系统          │
     * └─────────────────┴──────────────────────┴─────────────────────────┘
     *
     * 🔄 调用时机：
     * 1. handle() 方法的 catch 块中（所有异常都会调用）
     * 2. failed() 方法中（重试耗尽的最终失败）
     *
     * 🎯 监听组件示例：
     * - 统计服务：记录失败率、错误分类
     * - 监控服务：触发告警、性能分析
     * - 通知服务：发送管理员通知
     * - 日志服务：集中化错误日志
     *
     * @param string $errorMessage 错误消息
     * @return array 发布结果
     */
    private function publishFailureEvent(string $errorMessage): array
    {
        try {
            // 🌐 步骤1：构建HTTP请求，调用内部事件发布API
            // 使用内部服务令牌进行身份验证，确保安全性
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('INTERNAL_API_TOKEN', 'internal_service_token'),
                'Content-Type' => 'application/json'
            ])->withOptions([
                'verify' => false, // 跳过SSL证书验证（内部服务）
                'timeout' => 10    // 设置超时时间，避免阻塞
            ])->post(env('APP_URL') . '/py-api/events/publish', [
                // 📋 步骤2：构建事件数据结构
                'event_type' => EventTypeHelper::getFailedEventType($this->businessType),
                'business_id' => $this->taskId,    // 业务标识
                'user_id' => $this->userId,        // 用户标识
                'error_details' => [               // 错误详情
                    'error_message' => $errorMessage,
                    'task_id' => $this->taskId,
                    'context' => $this->context,
                    'prompt' => substr($this->prompt, 0, 200) // 截取提示词前200字符
                ],
                'metadata' => [                    // 元数据
                    'prompt' => $this->prompt,
                    'generation_params' => $this->generationParams,
                    'model_id' => $this->modelId,
                    'info_id' => $this->toolData[$this->businessType]['info_id'] ?? null,
                    'context' => $this->context,
                    'failed_at' => \Carbon\Carbon::now()->format('c')
                ]
            ]);

            // 📊 步骤3：处理API响应结果
            if ($response->successful()) {
                // ✅ 事件发布成功
                Log::info('文本生成失败事件发布成功', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'event_type' => EventTypeHelper::getFailedEventType($this->businessType),
                    'event_response' => $response->json(),
                    'listeners_notified' => '系统内部组件已收到失败通知'
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '文本生成失败事件发布成功',
                    'data' => [
                        'event_response' => $response->json(),
                        'event_type' => EventTypeHelper::getFailedEventType($this->businessType)
                    ]
                ];
            } else {
                // ❌ 事件发布失败（不影响主流程）
                Log::warning('文本生成失败事件发布失败', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'response_status' => $response->status(),
                    'response_body' => $response->body(),
                    'impact' => '事件发布失败不影响主业务流程'
                ]);

                return [
                    'code' => ApiCodeEnum::ERROR,
                    'message' => '文本生成失败事件发布失败',
                    'data' => [
                        'response_status' => $response->status(),
                        'response_body' => $response->body()
                    ]
                ];
            }

        } catch (\Exception $e) {
            // 🚨 步骤4：异常处理（网络错误、超时等）
            // 注意：事件发布失败不应该影响主业务流程
            Log::error('发布文本生成失败事件异常', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
                'impact' => '事件发布异常不影响主业务流程和用户体验'
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '发布文本生成失败事件异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 统一的进度推送方法 $this->businessType
     *
     * @param WebSocketEventService $webSocketEventService
     * @param int $progress 进度百分比
     * @param string $message 进度消息
     * @return array
     */
    private function pushProgress(WebSocketEventService $webSocketEventService, int $progress, string $message): array
    {
        Log::info('WebSocket推送进度', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'progress' => $progress,
            'message' => $message,
            'push_type' => 'ai_generation_progress',
            'timestamp' => time(),
            'job_class' => self::class,
            'job_method' => 'pushProgress'
        ]);

        $pushResult = $webSocketEventService->pushAiGenerationProgress(
            $this->taskId,
            $this->userId,
            $progress,
            $message,
            $this->businessType // 🔧 传入业务类型，避免数据库查询
        );

        Log::info('WebSocket推送结果', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'progress' => $progress,
            'message' => $message,
            'push_success' => $pushResult['code'] === ApiCodeEnum::SUCCESS ? 'yes' : 'no',
            'push_method' => 'Redis通道桥接',
            'push_message' => $pushResult['message'] ?? ''
        ]);

        return $pushResult;
    }
}
