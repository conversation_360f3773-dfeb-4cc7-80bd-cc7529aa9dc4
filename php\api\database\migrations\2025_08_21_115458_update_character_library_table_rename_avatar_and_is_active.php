<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 更新角色库表：将 avatar 字段重命名为 thumbnail，将 is_active 字段重命名为 status
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 第一步：重命名 avatar 字段为 thumbnail
        Schema::table('character_library', function (Blueprint $table) {
            $table->renameColumn('avatar', 'thumbnail');
        });

        // 第二步：删除 is_active 字段，添加 status 字段
        Schema::table('character_library', function (Blueprint $table) {
            $table->dropColumn('is_active');
            $table->enum('status', ['draft', 'in_progress', 'completed', 'published', 'archived'])
                  ->default('draft')
                  ->comment('状态')
                  ->after('tags');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 第一步：删除 status 字段，恢复 is_active 字段
        Schema::table('character_library', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->boolean('is_active')->default(true)->comment('是否激活')->after('tags');
        });

        // 第二步：将 thumbnail 字段重命名回 avatar
        Schema::table('character_library', function (Blueprint $table) {
            $table->renameColumn('thumbnail', 'avatar');
        });
    }
};
