<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 项目模型
 * 
 * @property int $id
 * @property int $user_id
 * @property string $title
 * @property string $description
 * @property int $style_id
 * @property string $story_content

 * @property bool $title_confirmed
 * @property string $status
 * @property array $project_config
 * @property array $metadata
 * @property \Carbon\Carbon $last_accessed_at
 * @property \Carbon\Carbon $completed_at
 * @property \Carbon\Carbon $published_at
 * @property int $view_count
 * @property bool $is_public
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Project extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'projects';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'title',
        'description',
        'style_id',
        'story_content',
        'title_confirmed',
        'status',
        'project_config',
        'metadata',
        'last_accessed_at',
        'completed_at',
        'published_at',
        'view_count',
        'is_public'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'title_confirmed' => 'boolean',
        'project_config' => 'array',
        'metadata' => 'array',
        'last_accessed_at' => 'datetime',
        'completed_at' => 'datetime',
        'published_at' => 'datetime',
        'view_count' => 'integer',
        'is_public' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'status' => 'draft',
        'title_confirmed' => false,
        'view_count' => 0,
        'is_public' => false
    ];

    /**
     * 状态常量
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_PUBLISHED = 'published';
    const STATUS_ARCHIVED = 'archived';
    const STATUS_ACTIVE = 'active';

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联风格
     */
    public function style(): BelongsTo
    {
        return $this->belongsTo(StyleLibrary::class, 'style_id');
    }

    /**
     * 关联项目场景
     */
    public function scenarios(): HasMany
    {
        return $this->hasMany(ProjectScenario::class)->orderBy('scene_order');
    }

    /**
     * 关联项目分镜
     */
    public function storyboards(): HasMany
    {
        return $this->hasMany(ProjectStoryboard::class)->orderBy('scene_number');
    }

    /**
     * 关联项目角色
     */
    public function characters(): HasMany
    {
        return $this->hasMany(ProjectCharacter::class);
    }



    /**
     * 更新访问时间
     */
    public function updateLastAccessed(): void
    {
        $this->last_accessed_at = Carbon::now();
        $this->save();
    }

    /**
     * 增加查看次数
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * 标记为完成
     */
    public function markAsCompleted(): void
    {
        $this->status = self::STATUS_COMPLETED;
        $this->completed_at = Carbon::now();
        $this->save();
    }

    /**
     * 发布项目
     */
    public function publish(): void
    {
        $this->status = self::STATUS_PUBLISHED;
        $this->published_at = Carbon::now();
        $this->is_public = true;
        $this->save();
    }

    /**
     * 归档项目
     */
    public function archive(): void
    {
        $this->status = self::STATUS_ARCHIVED;
        $this->save();
    }

    /**
     * 获取项目配置
     */
    public function getProjectConfig(string $key, $default = null)
    {
        return data_get($this->project_config, $key, $default);
    }

    /**
     * 设置项目配置
     */
    public function setProjectConfig(string $key, $value): void
    {
        $config = $this->project_config ?? [];
        data_set($config, $key, $value);
        $this->project_config = $config;
    }

    /**
     * 获取元数据
     */
    public function getMetadata(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * 设置元数据
     */
    public function setMetadata(string $key, $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
    }

    /**
     * 检查是否可以编辑
     */
    public function canEdit(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_IN_PROGRESS]);
    }

    /**
     * 检查是否已完成
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * 检查是否已发布
     */
    public function isPublished(): bool
    {
        return $this->status === self::STATUS_PUBLISHED;
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：公开的项目
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * 作用域：用户的项目
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：最近访问的项目
     */
    public function scopeRecentlyAccessed($query, $limit = 10)
    {
        return $query->whereNotNull('last_accessed_at')
            ->orderBy('last_accessed_at', 'desc')
            ->limit($limit);
    }

    /**
     * 作用域：热门项目
     */
    public function scopePopular($query, $limit = 10)
    {
        return $query->orderBy('view_count', 'desc')->limit($limit);
    }

    // ==================== 文件路径管理方法 ====================

    /**
     * 获取项目文件夹路径
     */
    public function getProjectFolderPath(string $subFolder = 'images'): string
    {
        return "projects/{$this->id}/{$subFolder}";
    }

    /**
     * 获取项目图片文件夹路径
     */
    public function getImagesFolderPath(): string
    {
        return $this->getProjectFolderPath('images');
    }

    /**
     * 获取项目视频文件夹路径
     */
    public function getVideosFolderPath(): string
    {
        return $this->getProjectFolderPath('videos');
    }

    /**
     * 获取项目音频文件夹路径
     */
    public function getAudioFolderPath(): string
    {
        return $this->getProjectFolderPath('audio');
    }

    /**
     * 获取项目分镜文件夹路径
     */
    public function getStoryboardsFolderPath(): string
    {
        return $this->getProjectFolderPath('storyboards');
    }

    /**
     * 获取项目导出文件夹路径
     */
    public function getExportsFolderPath(): string
    {
        return $this->getProjectFolderPath('exports');
    }

    /**
     * 获取项目临时文件夹路径
     */
    public function getTempFolderPath(): string
    {
        return $this->getProjectFolderPath('temp');
    }

    /**
     * 获取项目缩略图路径
     */
    public function getThumbnailPath(): string
    {
        return "projects/thumbnails/project_{$this->id}.jpg";
    }
}
