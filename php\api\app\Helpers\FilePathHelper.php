<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;

/**
 * 文件路径管理辅助类
 */
class FilePathHelper
{
    // 主要目录常量
    const FOLDER_PROJECTS = 'projects';
    const FOLDER_CHARACTERS = 'characters';
    const FOLDER_STYLES = 'styles';
    const FOLDER_VOICES = 'voices';
    const FOLDER_SOUNDS = 'sounds';
    const FOLDER_RESOURCES = 'resources';
    const FOLDER_USERS = 'users';
    const FOLDER_TEMP = 'temp';

    // 子目录常量
    const SUBFOLDER_IMAGES = 'images';
    const SUBFOLDER_VIDEOS = 'videos';
    const SUBFOLDER_AUDIO = 'audio';
    const SUBFOLDER_STORYBOARDS = 'storyboards';
    const SUBFOLDER_EXPORTS = 'exports';
    const SUBFOLDER_AVATARS = 'avatars';
    const SUBFOLDER_THUMBNAILS = 'thumbnails';
    const SUBFOLDER_SAMPLES = 'samples';
    const SUBFOLDER_MODELS = 'models';
    const SUBFOLDER_CONFIGS = 'configs';
    const SUBFOLDER_GENERATED = 'generated';
    const SUBFOLDER_LIBRARY = 'library';
    const SUBFOLDER_TEMPLATES = 'templates';
    const SUBFOLDER_UPLOADS = 'uploads';
    const SUBFOLDER_CACHE = 'cache';
    const SUBFOLDER_PROCESSING = 'processing';

    /**
     * 获取项目文件路径
     */
    public static function getProjectPath(int $projectId, string $subFolder = self::SUBFOLDER_IMAGES, string $fileName = null): string
    {
        $path = self::FOLDER_PROJECTS . "/{$projectId}/{$subFolder}";
        return $fileName ? "{$path}/{$fileName}" : $path;
    }

    /**
     * 获取角色文件路径
     */
    public static function getCharacterPath(string $subFolder = self::SUBFOLDER_AVATARS, string $fileName = null): string
    {
        $path = self::FOLDER_CHARACTERS . "/{$subFolder}";
        return $fileName ? "{$path}/{$fileName}" : $path;
    }

    /**
     * 获取风格文件路径
     */
    public static function getStylePath(string $subFolder = self::SUBFOLDER_THUMBNAILS, string $fileName = null): string
    {
        $path = self::FOLDER_STYLES . "/{$subFolder}";
        return $fileName ? "{$path}/{$fileName}" : $path;
    }

    /**
     * 获取音色文件路径
     */
    public static function getVoicePath(string $subFolder = self::SUBFOLDER_SAMPLES, string $fileName = null): string
    {
        $path = self::FOLDER_VOICES . "/{$subFolder}";
        return $fileName ? "{$path}/{$fileName}" : $path;
    }

    /**
     * 获取音效文件路径
     */
    public static function getSoundPath(string $subFolder = self::SUBFOLDER_GENERATED, string $fileName = null): string
    {
        $path = self::FOLDER_SOUNDS . "/{$subFolder}";
        return $fileName ? "{$path}/{$fileName}" : $path;
    }

    /**
     * 获取资源文件路径
     */
    public static function getResourcePath(string $subFolder = self::SUBFOLDER_GENERATED, string $fileName = null): string
    {
        $path = self::FOLDER_RESOURCES . "/{$subFolder}";
        return $fileName ? "{$path}/{$fileName}" : $path;
    }

    /**
     * 获取用户文件路径
     */
    public static function getUserPath(int $userId, string $subFolder = self::SUBFOLDER_UPLOADS, string $fileName = null): string
    {
        $path = self::FOLDER_USERS . "/{$userId}/{$subFolder}";
        return $fileName ? "{$path}/{$fileName}" : $path;
    }

    /**
     * 获取临时文件路径
     */
    public static function getTempPath(string $subFolder = self::SUBFOLDER_UPLOADS, string $fileName = null): string
    {
        $path = self::FOLDER_TEMP . "/{$subFolder}";
        return $fileName ? "{$path}/{$fileName}" : $path;
    }

    /**
     * 根据资源类型获取对应路径
     */
    public static function getResourcePathByType(string $resourceType, string $fileName = null): string
    {
        $subFolder = match($resourceType) {
            'image' => self::SUBFOLDER_GENERATED . '/' . self::SUBFOLDER_IMAGES,
            'video' => self::SUBFOLDER_GENERATED . '/' . self::SUBFOLDER_VIDEOS,
            'voice', 'audio' => self::SUBFOLDER_GENERATED . '/' . self::SUBFOLDER_AUDIO,
            'music' => self::SUBFOLDER_GENERATED . '/music',
            'sound' => self::SUBFOLDER_GENERATED . '/sounds',
            'story' => self::SUBFOLDER_GENERATED . '/stories',
            default => self::SUBFOLDER_GENERATED
        };
        
        return self::getResourcePath($subFolder, $fileName);
    }

    /**
     * 生成唯一文件名
     */
    public static function generateUniqueFileName(string $originalName, string $prefix = 'file'): string
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $timestamp = date('Ymd_His');
        $random = substr(md5(uniqid()), 0, 8);
        
        return "{$prefix}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * 获取文件的完整URL
     */
    public static function getFileUrl(string $filePath): string
    {
        return Storage::url($filePath);
    }

    /**
     * 检查文件是否存在
     */
    public static function fileExists(string $filePath): bool
    {
        return Storage::disk('public')->exists($filePath);
    }

    /**
     * 删除文件
     */
    public static function deleteFile(string $filePath): bool
    {
        if (self::fileExists($filePath)) {
            return Storage::disk('public')->delete($filePath);
        }
        return true;
    }

    /**
     * 创建目录
     */
    public static function createDirectory(string $directoryPath): bool
    {
        return Storage::disk('public')->makeDirectory($directoryPath);
    }

    /**
     * 获取文件大小（字节）
     */
    public static function getFileSize(string $filePath): int
    {
        if (self::fileExists($filePath)) {
            return Storage::disk('public')->size($filePath);
        }
        return 0;
    }

    /**
     * 获取人类可读的文件大小
     */
    public static function getHumanFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 验证文件扩展名
     */
    public static function validateFileExtension(string $fileName, array $allowedExtensions): bool
    {
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        return in_array($extension, array_map('strtolower', $allowedExtensions));
    }

    /**
     * 获取MIME类型对应的文件扩展名
     */
    public static function getExtensionFromMimeType(string $mimeType): string
    {
        $mimeToExt = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'audio/mpeg' => 'mp3',
            'audio/wav' => 'wav',
            'audio/ogg' => 'ogg',
            'video/mp4' => 'mp4',
            'video/webm' => 'webm',
            'video/ogg' => 'ogv',
            'application/pdf' => 'pdf',
            'text/plain' => 'txt',
            'application/json' => 'json',
        ];
        
        return $mimeToExt[$mimeType] ?? 'bin';
    }

    /**
     * 清理文件名（移除特殊字符）
     */
    public static function sanitizeFileName(string $fileName): string
    {
        // 保留扩展名
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $name = pathinfo($fileName, PATHINFO_FILENAME);
        
        // 移除特殊字符，只保留字母、数字、下划线和连字符
        $name = preg_replace('/[^a-zA-Z0-9_\-\u4e00-\u9fa5]/u', '_', $name);
        $name = preg_replace('/_+/', '_', $name); // 合并多个下划线
        $name = trim($name, '_'); // 移除首尾下划线
        
        return $name . ($extension ? ".{$extension}" : '');
    }
}
