<?php

namespace App\Jobs;

use App\Models\CharacterLibrary;
use App\Services\PyApi\CharacterService;
use App\Services\PyApi\WebSocketEventService;
use App\Services\PyApi\PointsService;
use App\Helpers\EventTypeHelper;
use App\Enums\ApiCodeEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

/**
 * 异步处理角色创建任务
 * 实现图表中的完整 WebSocket 进度推送流程
 */
class ProcessCharacterCreation implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $taskId;
    protected $userId;
    protected $characterData;
    protected $projectId;
    protected $mode;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 300; // 5分钟

    /**
     * 最大重试次数
     */
    public $tries = 3;

    public function __construct(string $taskId, int $userId, array $characterData, ?int $projectId = null, string $mode = 'intelligent')
    {
        $this->taskId = $taskId;
        $this->userId = $userId;
        $this->characterData = $characterData;
        $this->projectId = $projectId;
        $this->mode = $mode;
    }

    /**
     * 执行任务
     */
    public function handle()
    {
        $webSocketEventService = app(WebSocketEventService::class);
        $characterService = app(CharacterService::class);
        $pointsService = app(PointsService::class);

        try {
            DB::beginTransaction();

            // 推送任务开始进度
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                10,
                "开始角色创建任务"
            );

            // 估算成本
            $estimatedCost = $this->calculateCharacterCreationCost();

            // 冻结积分
            $freezeResult = $pointsService->freezePoints(
                $this->userId,
                $estimatedCost,
                'character_creation',
                $this->taskId,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                throw new \Exception('积分冻结失败：' . $freezeResult['message']);
            }

            $freezeId = $freezeResult['data']['freeze_id'] ?? null;

            // 推送进度更新
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                30,
                $this->mode === 'intelligent' ? "连接AI平台生成角色图像" : "处理上传的角色图片"
            );

            // 执行角色创建
            if ($this->mode === 'intelligent') {
                // 智能模式：AI生成业务逻辑应该在这里实现，而不是在服务层
                // TODO: 实现AI生成角色的业务逻辑
                $result = [
                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                    'message' => 'AI生成功能暂未实现，请使用自定义模式',
                    'data' => []
                ];
            } else {
                // 自定义模式：基于文件创建角色
                $result = $characterService->createCharacterFromFile(
                    $this->userId,
                    $this->projectId,
                    $this->characterData
                );
            }

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 创建成功
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    80,
                    "保存角色数据"
                );

                // 确认积分扣取
                if ($freezeId) {
                    $confirmResult = $pointsService->confirmPointsUsage(
                        $this->userId,
                        $estimatedCost,
                        'character_creation',
                        $result['data']['character_id']
                    );

                    if ($confirmResult['code'] !== ApiCodeEnum::SUCCESS) {
                        Log::warning('角色创建成功但积分确认失败', [
                            'task_id' => $this->taskId,
                            'user_id' => $this->userId,
                            'character_id' => $result['data']['character_id'],
                            'error' => $confirmResult['message']
                        ]);
                    }
                }

                // 推送完成进度
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    100,
                    "角色创建完成"
                );

                // 推送任务完成事件
                $webSocketEventService->pushAiGenerationCompleted(
                    $this->taskId,
                    $this->userId
                );

                DB::commit();

                Log::info('角色创建任务完成', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'character_id' => $result['data']['character_id'],
                    'mode' => $this->mode,
                    'cost' => $estimatedCost
                ]);

            } else {
                // 创建失败
                if ($freezeId) {
                    $refundResult = $pointsService->refundPointsByFreezeId(
                        $this->userId,
                        $freezeId,
                        '角色创建失败，自动退还积分'
                    );

                    if ($refundResult['code'] !== ApiCodeEnum::SUCCESS) {
                        Log::error('角色创建失败后积分退还失败', [
                            'task_id' => $this->taskId,
                            'user_id' => $this->userId,
                            'freeze_id' => $freezeId,
                            'error' => $refundResult['message']
                        ]);
                    }
                }

                throw new \Exception($result['message']);
            }

        } catch (\Exception $e) {
            DB::rollBack();

            // 推送任务失败事件
            $webSocketEventService->pushAiGenerationFailed(
                $this->taskId,
                $this->userId,
                $e->getMessage()
            );

            // 发布失败事件到事件总线
            $this->publishFailureEvent($e->getMessage());

            Log::error('角色创建任务失败', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'mode' => $this->mode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 任务失败处理
     */
    public function failed(\Throwable $exception)
    {
        $webSocketEventService = app(WebSocketEventService::class);

        // 推送任务失败事件
        $webSocketEventService->pushAiGenerationFailed(
            $this->taskId,
            $this->userId,
            $exception->getMessage()
        );

        // 发布最终失败事件
        $this->publishFailureEvent($exception->getMessage());

        Log::error('角色创建任务最终失败', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'error' => $exception->getMessage()
        ]);
    }

    /**
     * 计算角色创建成本
     */
    private function calculateCharacterCreationCost(): float
    {
        // 根据创建模式计算成本
        if ($this->mode === 'intelligent') {
            // 智能角色模式需要AI生成图像，成本较高
            return 15.0;
        } else {
            // 自有角色模式只需要处理上传图片，成本较低
            return 5.0;
        }
    }

    /**
     * 发布失败事件到事件总线
     */
    private function publishFailureEvent(string $errorMessage): void
    {
        try {
            // 调用事件发布API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('INTERNAL_API_TOKEN', 'internal_service_token'),
                'Content-Type' => 'application/json'
            ])->post(env('APP_URL') . '/py-api/events/publish', [
                'event_type' => EventTypeHelper::getFailedEventType('character_creation') ?? 'character_creation_failed',
                'business_id' => $this->taskId,
                'user_id' => $this->userId,
                'error_details' => [
                    'error_message' => $errorMessage,
                    'task_id' => $this->taskId,
                    'mode' => $this->mode,
                    'creation_type' => $this->characterData['character_type'] ?? 'unknown'
                ],
                'metadata' => [
                    'character_data' => $this->characterData,
                    'project_id' => $this->projectId,
                    'mode' => $this->mode,
                    'failed_at' => now()->format('c')
                ]
            ]);

            if ($response->successful()) {
                Log::info('角色创建失败事件发布成功', [
                    'task_id' => $this->taskId,
                    'event_response' => $response->json()
                ]);
            } else {
                Log::warning('角色创建失败事件发布失败', [
                    'task_id' => $this->taskId,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('发布角色创建失败事件异常', [
                'task_id' => $this->taskId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
