<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 剧情风格库模型
 * 
 * @property int $id
 * @property string $name
 * @property string $description
 * @property string $ai_prompt
 * @property array $images
 * @property string $thumbnail
 * @property string $status
 * @property bool $is_premium
 * @property int $sort_order
 * @property int $usage_count
 * @property float $rating
 * @property array $tags
 * @property int $created_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class StyleLibrary extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'style_library';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'description',
        'ai_prompt',
        'images',
        'thumbnail',
        'status',
        'is_premium',
        'sort_order',
        'usage_count',
        'rating',
        'tags',
        'created_by'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'images' => 'array',
        'status' => 'string',
        'is_premium' => 'boolean',
        'sort_order' => 'integer',
        'usage_count' => 'integer',
        'rating' => 'decimal:2',
        'tags' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'status' => 'draft',
        'is_premium' => false,
        'sort_order' => 0,
        'usage_count' => 0,
        'rating' => 0
    ];

    /**
     * 状态常量
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_PUBLISHED = 'published';
    const STATUS_ARCHIVED = 'archived';

    /**
     * 关联创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 关联项目
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class, 'style_id');
    }

    /**
     * 增加使用次数
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * 更新评分
     */
    public function updateRating(float $newRating): void
    {
        $this->rating = $newRating;
        $this->save();
    }

    /**
     * 获取风格参数
     */
    public function getStyleParams(string $key, $default = null)
    {
        return data_get($this->style_params, $key, $default);
    }

    /**
     * 设置风格参数
     */
    public function setStyleParams(string $key, $value): void
    {
        $params = $this->style_params ?? [];
        data_set($params, $key, $value);
        $this->style_params = $params;
    }

    /**
     * 获取风格配置（向后兼容）
     * @deprecated 使用 getStyleParams() 替代
     */
    public function getStyleConfig(string $key, $default = null)
    {
        return $this->getStyleParams($key, $default);
    }

    /**
     * 设置风格配置（向后兼容）
     * @deprecated 使用 setStyleParams() 替代
     */
    public function setStyleConfig(string $key, $value): void
    {
        $this->setStyleParams($key, $value);
    }

    /**
     * 检查是否包含标签
     */
    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags ?? []);
    }

    /**
     * 添加标签
     */
    public function addTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->tags = $tags;
        }
    }

    /**
     * 移除标签
     */
    public function removeTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        $this->tags = array_values(array_filter($tags, fn($t) => $t !== $tag));
    }

    /**
     * 获取风格图集
     */
    public function getImages(): array
    {
        return $this->images ?? [];
    }

    /**
     * 添加风格图片
     */
    public function addImage(string $imageUrl): void
    {
        $images = $this->images ?? [];
        if (!in_array($imageUrl, $images)) {
            $images[] = $imageUrl;
            $this->images = $images;
        }
    }

    /**
     * 移除风格图片
     */
    public function removeImage(string $imageUrl): void
    {
        $images = $this->images ?? [];
        $this->images = array_values(array_filter($images, fn($img) => $img !== $imageUrl));
    }

    /**
     * 设置风格图集
     */
    public function setImages(array $images): void
    {
        $this->images = array_values(array_unique($images));
    }

    /**
     * 作用域：已发布的风格
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：高级风格
     */
    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * 作用域：免费风格
     */
    public function scopeFree($query)
    {
        return $query->where('is_premium', false);
    }

    /**
     * 作用域：按排序权重排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'desc')->orderBy('usage_count', 'desc');
    }

    /**
     * 作用域：热门风格
     */
    public function scopePopular($query, $limit = 10)
    {
        return $query->orderBy('usage_count', 'desc')->limit($limit);
    }

    /**
     * 作用域：高评分风格
     */
    public function scopeHighRated($query, $minRating = 4.0)
    {
        return $query->where('rating', '>=', $minRating)->orderBy('rating', 'desc');
    }

    // ==================== 文件路径管理方法 ====================

    /**
     * 获取风格文件夹路径
     */
    public function getStyleFolderPath(string $subFolder = 'thumbnails'): string
    {
        return "styles/{$subFolder}";
    }

    /**
     * 获取风格缩略图文件夹路径
     */
    public function getThumbnailsFolderPath(): string
    {
        return $this->getStyleFolderPath('thumbnails');
    }

    /**
     * 获取风格样例文件夹路径
     */
    public function getSamplesFolderPath(): string
    {
        return $this->getStyleFolderPath('samples');
    }

    /**
     * 获取风格模板文件夹路径
     */
    public function getTemplatesFolderPath(): string
    {
        return $this->getStyleFolderPath('templates');
    }

    /**
     * 获取风格缩略图完整路径
     */
    public function getThumbnailPath(): string
    {
        if ($this->thumbnail) {
            return $this->thumbnail;
        }
        return $this->getThumbnailsFolderPath() . "/style_{$this->id}.jpg";
    }
}
