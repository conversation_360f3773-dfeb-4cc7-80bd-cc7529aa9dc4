<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 角色库模型
 *
 * @property int $id
 * @property string $name
 * @property string $category
 * @property string $gender
 * @property string $age_range
 * @property string $ai_prompt
 * @property string $thumbnail
 * @property array $images
 * @property array $tags
 * @property string $status
 * @property bool $is_premium
 * @property bool $is_featured
 * @property int $sort_order
 * @property int $binding_count
 * @property float $rating
 * @property int $rating_count
 * @property int $created_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class CharacterLibrary extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'character_library';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'category',
        'gender',
        'age_range',
        'ai_prompt',
        'thumbnail',
        'images',
        'tags',
        'status',
        'is_premium',
        'is_featured',
        'sort_order',
        'binding_count',
        'rating',
        'rating_count',
        'created_by'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'images' => 'array',
        'tags' => 'array',
        'status' => 'string',
        'is_premium' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
        'binding_count' => 'integer',
        'rating' => 'decimal:2',
        'rating_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'status' => 'draft',
        'is_premium' => false,
        'is_featured' => false,
        'sort_order' => 0,
        'binding_count' => 0,
        'rating' => 0,
        'rating_count' => 0
    ];

    /**
     * 分类常量
     */
    const CATEGORY_HUMAN = '人类';
    const CATEGORY_ANTHROPOMORPHIC = '拟人动物';
    const CATEGORY_ANIMAL = '原生动物';

    /**
     * 年龄范围常量
     */
    const AGE_RANGE_INFANT = '婴幼儿(0-6岁)';
    const AGE_RANGE_CHILD = '儿童(6-12岁)';
    const AGE_RANGE_TEENAGER = '少年(12-18岁)';
    const AGE_RANGE_YOUTH = '青年(18-40岁)';
    const AGE_RANGE_MIDDLE_AGED = '中年(40-65岁)';
    const AGE_RANGE_ELDERLY = '老年(65岁以上)';

    /**
     * 性别常量
     */
    const GENDER_MALE = '男性';
    const GENDER_FEMALE = '女性';
    const GENDER_OTHER = '未知';



    /**
     * 关联创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 关联用户绑定
     */
    public function userBindings(): HasMany
    {
        return $this->hasMany(UserCharacterBinding::class, 'character_id');
    }

    /**
     * 增加绑定次数
     */
    public function incrementBinding(): void
    {
        $this->increment('binding_count');
    }

    /**
     * 减少绑定次数
     */
    public function decrementBinding(): void
    {
        $this->decrement('binding_count');
    }

    /**
     * 更新评分
     */
    public function updateRating(float $newRating): void
    {
        $totalRating = ($this->rating * $this->rating_count) + $newRating;
        $this->rating_count++;
        $this->rating = $totalRating / $this->rating_count;
        $this->save();
    }

    /**
     * 检查是否包含标签
     */
    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags ?? []);
    }

    /**
     * 添加标签
     */
    public function addTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->tags = $tags;
        }
    }

    /**
     * 移除标签
     */
    public function removeTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        $this->tags = array_values(array_filter($tags, fn($t) => $t !== $tag));
    }



    /**
     * 获取完整角色信息
     */
    public function getFullProfile(): array
    {
        return [
            'basic_info' => [
                'name' => $this->name,
                'ai_prompt' => $this->ai_prompt,
                'gender' => $this->gender,
                'age_range' => $this->age_range
            ],
            'category' => $this->category,
            'tags' => $this->tags ?? []
        ];
    }

    /**
     * 作用域：已发布的角色
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：按性别筛选
     */
    public function scopeByGender($query, $gender)
    {
        return $query->where('gender', $gender);
    }

    /**
     * 作用域：高级角色
     */
    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * 作用域：免费角色
     */
    public function scopeFree($query)
    {
        return $query->where('is_premium', false);
    }

    /**
     * 作用域：推荐角色
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * 作用域：按排序权重排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'desc')
            ->orderBy('binding_count', 'desc')
            ->orderBy('rating', 'desc');
    }

    /**
     * 作用域：热门角色
     */
    public function scopePopular($query, $limit = 10)
    {
        return $query->orderBy('binding_count', 'desc')->limit($limit);
    }

    /**
     * 作用域：高评分角色
     */
    public function scopeHighRated($query, $minRating = 4.0)
    {
        return $query->where('rating', '>=', $minRating)
            ->where('rating_count', '>=', 5)
            ->orderBy('rating', 'desc');
    }

    /**
     * 作用域：按标签筛选
     */
    public function scopeByTag($query, $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    /**
     * 作用域：搜索
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('ai_prompt', 'like', "%{$keyword}%")
              ->orWhereJsonContains('tags', $keyword);
        });
    }

    // ==================== 文件路径管理方法 ====================

    /**
     * 获取角色文件夹路径
     */
    public function getCharacterFolderPath(string $subFolder = 'avatars'): string
    {
        return "characters/{$subFolder}";
    }

    /**
     * 获取角色头像文件夹路径
     */
    public function getAvatarsFolderPath(): string
    {
        return $this->getCharacterFolderPath('avatars');
    }

    /**
     * 获取角色图片文件夹路径
     */
    public function getImagesFolderPath(): string
    {
        return $this->getCharacterFolderPath('images');
    }

    /**
     * 获取角色音色文件夹路径
     */
    public function getVoicesFolderPath(): string
    {
        return $this->getCharacterFolderPath('voices');
    }

    /**
     * 获取角色配置文件夹路径
     */
    public function getConfigsFolderPath(): string
    {
        return $this->getCharacterFolderPath('configs');
    }

    /**
     * 获取角色缩略图完整路径
     */
    public function getThumbnailPath(): string
    {
        if ($this->thumbnail) {
            return $this->thumbnail;
        }
        return $this->getAvatarsFolderPath() . "/character_{$this->id}.jpg";
    }

    /**
     * 获取角色头像完整路径（向后兼容）
     * @deprecated 使用 getThumbnailPath() 替代
     */
    public function getAvatarPath(): string
    {
        return $this->getThumbnailPath();
    }

    /**
     * 获取所有分类选项
     */
    public static function getCategories(): array
    {
        return [
            self::CATEGORY_HUMAN,
            self::CATEGORY_ANTHROPOMORPHIC,
            self::CATEGORY_ANIMAL,
        ];
    }

    /**
     * 获取所有年龄范围选项
     */
    public static function getAgeRanges(): array
    {
        return [
            self::AGE_RANGE_INFANT,
            self::AGE_RANGE_CHILD,
            self::AGE_RANGE_TEENAGER,
            self::AGE_RANGE_YOUTH,
            self::AGE_RANGE_MIDDLE_AGED,
            self::AGE_RANGE_ELDERLY,
        ];
    }

    /**
     * 获取年龄范围的简短描述
     */
    public static function getAgeRangeDescription(string $ageRange): string
    {
        $descriptions = [
            self::AGE_RANGE_INFANT => '0-6岁',
            self::AGE_RANGE_CHILD => '6-12岁',
            self::AGE_RANGE_TEENAGER => '12-18岁',
            self::AGE_RANGE_YOUTH => '18-40岁',
            self::AGE_RANGE_MIDDLE_AGED => '40-65岁',
            self::AGE_RANGE_ELDERLY => '65岁以上',
        ];

        return $descriptions[$ageRange] ?? '';
    }
}
