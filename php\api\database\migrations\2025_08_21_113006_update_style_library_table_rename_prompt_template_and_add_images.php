<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 更新风格库表：将 prompt_template 字段重命名为 ai_prompt，并增加 images 字段
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 第一步：重命名 prompt_template 字段为 ai_prompt
        Schema::table('style_library', function (Blueprint $table) {
            $table->renameColumn('prompt_template', 'ai_prompt');
        });

        // 第二步：增加 images 字段
        Schema::table('style_library', function (Blueprint $table) {
            $table->json('images')->nullable()->comment('风格图集')->after('ai_prompt');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('style_library', function (Blueprint $table) {
            // 删除 images 字段
            $table->dropColumn('images');

            // 将 ai_prompt 字段重命名回 prompt_template
            $table->renameColumn('ai_prompt', 'prompt_template');
        });
    }
};
