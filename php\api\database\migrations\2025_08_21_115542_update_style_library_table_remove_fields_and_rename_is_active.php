<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 更新风格库表：删除 style_config 和 category 字段，将 is_active 字段重命名为 status
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('style_library', function (Blueprint $table) {
            // 删除 style_config 和 category 字段
            $table->dropColumn(['style_config', 'category']);

            // 删除 is_active 字段，添加 status 字段
            $table->dropColumn('is_active');
            $table->enum('status', ['draft', 'in_progress', 'completed', 'published', 'archived'])
                  ->default('draft')
                  ->comment('状态')
                  ->after('thumbnail');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('style_library', function (Blueprint $table) {
            // 删除 status 字段，恢复 is_active 字段
            $table->dropColumn('status');
            $table->boolean('is_active')->default(true)->comment('是否激活')->after('thumbnail');

            // 恢复 style_config 和 category 字段
            $table->json('style_config')->nullable()->comment('风格配置参数')->after('description');
            $table->string('category', 50)->default('general')->comment('风格分类')->after('description');
        });
    }
};
