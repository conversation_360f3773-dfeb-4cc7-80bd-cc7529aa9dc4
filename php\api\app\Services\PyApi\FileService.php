<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\UserFile;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * 文件管理服务
 */
class FileService
{
    // 预定义目录常量
    const FOLDER_PROJECTS = 'projects';
    const FOLDER_CHARACTERS = 'characters';
    const FOLDER_STYLES = 'styles';
    const FOLDER_VOICES = 'voices';
    const FOLDER_SOUNDS = 'sounds';
    const FOLDER_RESOURCES = 'resources';
    const FOLDER_USERS = 'users';
    const FOLDER_TEMP = 'temp';

    // 子目录常量
    const SUBFOLDER_IMAGES = 'images';
    const SUBFOLDER_VIDEOS = 'videos';
    const SUBFOLDER_AUDIO = 'audio';
    const SUBFOLDER_STORYBOARDS = 'storyboards';
    const SUBFOLDER_EXPORTS = 'exports';
    const SUBFOLDER_AVATARS = 'avatars';
    const SUBFOLDER_THUMBNAILS = 'thumbnails';
    const SUBFOLDER_SAMPLES = 'samples';
    const SUBFOLDER_MODELS = 'models';
    const SUBFOLDER_CONFIGS = 'configs';
    const SUBFOLDER_GENERATED = 'generated';
    const SUBFOLDER_LIBRARY = 'library';
    const SUBFOLDER_TEMPLATES = 'templates';
    const SUBFOLDER_UPLOADS = 'uploads';
    const SUBFOLDER_CACHE = 'cache';
    const SUBFOLDER_PROCESSING = 'processing';
    /**
     * 上传文件
     */
    public function uploadFile(int $userId, UploadedFile $file, ?string $folder = null, bool $isPublic = false, bool $isTemporary = false): array
    {
        try {
            // 验证文件
            if (!$file->isValid()) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '文件上传失败',
                    'data' => []
                ];
            }

            // 检查文件类型
            $allowedMimes = [
                'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                'audio/mpeg', 'audio/wav', 'audio/ogg',
                'video/mp4', 'video/webm', 'video/ogg',
                'application/pdf', 'text/plain', 'application/json',
                'application/zip', 'application/x-rar-compressed'
            ];

            if (!in_array($file->getMimeType(), $allowedMimes)) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '不支持的文件类型',
                    'data' => []
                ];
            }

            // 生成文件名
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $filename = 'file_' . date('Ymd_His') . '_' . Str::random(8) . '.' . $extension;

            // 确定存储路径
            $folderPath = $folder ? trim($folder, '/') : 'uploads';
            $storagePath = $folderPath . '/' . $filename;

            // 存储文件
            $path = $file->storeAs($folderPath, $filename, 'public');
            if (!$path) {
                return [
                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                    'message' => '文件存储失败',
                    'data' => []
                ];
            }

            // 计算文件哈希
            $fileHash = hash_file('sha256', $file->getPathname());

            // 确定文件类型
            $fileType = $this->getFileType($file->getMimeType());

            // 创建文件记录
            $userFile = UserFile::create([
                'user_id' => $userId,
                'filename' => $filename,
                'original_name' => $originalName,
                'file_path' => $path,
                'file_url' => Storage::url($path),
                'file_type' => $fileType,
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'file_hash' => $fileHash,
                'storage_driver' => 'public',
                'folder_path' => $folderPath,
                'is_public' => $isPublic,
                'is_temporary' => $isTemporary,
                'expires_at' => $isTemporary ? Carbon::now()->addDays(1) : null
            ]);

            // 生成缩略图（如果是图片）
            if ($fileType === UserFile::TYPE_IMAGE) {
                $this->generateThumbnails($userFile);
            }

            Log::info('文件上传成功', [
                'user_id' => $userId,
                'file_id' => $userFile->id,
                'filename' => $filename,
                'file_size' => $file->getSize()
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '文件上传成功',
                'data' => [
                    'id' => $userFile->id,
                    'filename' => $userFile->filename,
                    'original_name' => $userFile->original_name,
                    'file_url' => $userFile->full_url,
                    'file_type' => $userFile->file_type,
                    'file_size' => $userFile->file_size,
                    'human_file_size' => $userFile->human_file_size,
                    'created_at' => $userFile->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'file_name' => $file->getClientOriginalName(),
                'folder' => $folder,
                'is_public' => $isPublic,
                'is_temporary' => $isTemporary,
            ];

            Log::error('文件上传失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文件上传失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户文件列表
     */
    public function getUserFiles(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = UserFile::byUser($userId);

            // 应用筛选条件
            if (!empty($filters['folder'])) {
                $query->byFolder($filters['folder']);
            }

            if (!empty($filters['file_type'])) {
                $query->byType($filters['file_type']);
            }

            if (!empty($filters['keyword'])) {
                $query->search($filters['keyword']);
            }

            $files = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $filesData = $files->map(function ($file) {
                return [
                    'id' => $file->id,
                    'filename' => $file->filename,
                    'original_name' => $file->original_name,
                    'file_url' => $file->full_url,
                    'file_type' => $file->file_type,
                    'mime_type' => $file->mime_type,
                    'file_size' => $file->file_size,
                    'human_file_size' => $file->human_file_size,
                    'download_count' => $file->download_count,
                    'is_public' => $file->is_public,
                    'is_temporary' => $file->is_temporary,
                    'created_at' => $file->created_at->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'files' => $filesData,
                    'pagination' => [
                        'current_page' => $files->currentPage(),
                        'total' => $files->total(),
                        'per_page' => $files->perPage(),
                        'last_page' => $files->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
            ];

            Log::error('获取文件列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取文件列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取文件详情
     */
    public function getFileDetail(int $fileId, int $userId): array
    {
        try {
            $file = UserFile::byUser($userId)->find($fileId);

            if (!$file) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '文件不存在',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'id' => $file->id,
                    'filename' => $file->filename,
                    'original_name' => $file->original_name,
                    'file_url' => $file->full_url,
                    'file_type' => $file->file_type,
                    'mime_type' => $file->mime_type,
                    'file_size' => $file->file_size,
                    'human_file_size' => $file->human_file_size,
                    'file_hash' => $file->file_hash,
                    'folder_path' => $file->folder_path,
                    'download_count' => $file->download_count,
                    'is_public' => $file->is_public,
                    'is_temporary' => $file->is_temporary,
                    'metadata' => $file->metadata ?? [],
                    'thumbnails' => $file->thumbnails ?? [],
                    'last_accessed_at' => $file->last_accessed_at?->format('Y-m-d H:i:s'),
                    'expires_at' => $file->expires_at?->format('Y-m-d H:i:s'),
                    'created_at' => $file->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'file_id' => $fileId,
                'user_id' => $userId,
            ];

            Log::error('获取文件详情失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取文件详情失败',
                'data' => null
            ];
        }
    }

    /**
     * 删除文件
     */
    public function deleteFile(int $fileId, int $userId): array
    {
        try {
            $file = UserFile::byUser($userId)->find($fileId);

            if (!$file) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '文件不存在',
                    'data' => []
                ];
            }

            // 删除物理文件
            $file->deletePhysicalFile();

            // 删除数据库记录
            $file->delete();

            Log::info('文件删除成功', [
                'file_id' => $fileId,
                'user_id' => $userId,
                'filename' => $file->filename
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '文件删除成功',
                'data' => [
                    'file_id' => $fileId,
                    'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'file_id' => $fileId,
                'user_id' => $userId,
            ];

            Log::error('文件删除失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文件删除失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取下载URL
     */
    public function getDownloadUrl(int $fileId, int $userId): array
    {
        try {
            $file = UserFile::byUser($userId)->find($fileId);

            if (!$file) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '文件不存在',
                    'data' => []
                ];
            }

            if ($file->isExpired()) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '文件已过期',
                    'data' => []
                ];
            }

            // 增加下载次数
            $file->incrementDownload();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'download_url' => $file->full_url,
                    'filename' => $file->original_name,
                    'file_size' => $file->file_size,
                    'human_file_size' => $file->human_file_size,
                    'expires_at' => $file->expires_at?->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'file_id' => $fileId,
                'user_id' => $userId,
            ];

            Log::error('获取下载URL失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取下载URL失败',
                'data' => null
            ];
        }
    }

    /**
     * 确定文件类型
     */
    private function getFileType(string $mimeType): string
    {
        if (str_starts_with($mimeType, 'image/')) {
            return UserFile::TYPE_IMAGE;
        } elseif (str_starts_with($mimeType, 'audio/')) {
            return UserFile::TYPE_AUDIO;
        } elseif (str_starts_with($mimeType, 'video/')) {
            return UserFile::TYPE_VIDEO;
        } elseif (in_array($mimeType, ['application/pdf', 'text/plain', 'application/json'])) {
            return UserFile::TYPE_DOCUMENT;
        } elseif (in_array($mimeType, ['application/zip', 'application/x-rar-compressed'])) {
            return UserFile::TYPE_ARCHIVE;
        } else {
            return UserFile::TYPE_OTHER;
        }
    }

    /**
     * 生成缩略图（模拟实现）
     */
    private function generateThumbnails(UserFile $file): void
    {
        // 这里应该实现真实的缩略图生成逻辑
        // 目前模拟设置缩略图URL
        $file->setThumbnail('small', $file->full_url);
        $file->setThumbnail('medium', $file->full_url);
        $file->setThumbnail('large', $file->full_url);
        $file->save();
    }

    /**
     * 上传项目相关文件
     */
    public function uploadProjectFile(int $userId, int $projectId, UploadedFile $file, string $subFolder = self::SUBFOLDER_IMAGES, bool $isPublic = true, bool $isTemporary = false): array
    {
        $folder = self::FOLDER_PROJECTS . "/{$projectId}/{$subFolder}";
        return $this->uploadFile($userId, $file, $folder, $isPublic, $isTemporary);
    }

    /**
     * 上传角色相关文件
     */
    public function uploadCharacterFile(int $userId, UploadedFile $file, string $subFolder = self::SUBFOLDER_AVATARS, bool $isPublic = true, bool $isTemporary = false): array
    {
        $folder = self::FOLDER_CHARACTERS . "/{$subFolder}";
        return $this->uploadFile($userId, $file, $folder, $isPublic, $isTemporary);
    }

    /**
     * 上传风格相关文件
     */
    public function uploadStyleFile(int $userId, UploadedFile $file, string $subFolder = self::SUBFOLDER_THUMBNAILS, bool $isPublic = true, bool $isTemporary = false): array
    {
        $folder = self::FOLDER_STYLES . "/{$subFolder}";
        return $this->uploadFile($userId, $file, $folder, $isPublic, $isTemporary);
    }

    /**
     * 上传音色相关文件
     */
    public function uploadVoiceFile(int $userId, UploadedFile $file, string $subFolder = self::SUBFOLDER_SAMPLES, bool $isPublic = true, bool $isTemporary = false): array
    {
        $folder = self::FOLDER_VOICES . "/{$subFolder}";
        return $this->uploadFile($userId, $file, $folder, $isPublic, $isTemporary);
    }

    /**
     * 上传音效相关文件
     */
    public function uploadSoundFile(int $userId, UploadedFile $file, string $subFolder = self::SUBFOLDER_GENERATED, bool $isPublic = true, bool $isTemporary = false): array
    {
        $folder = self::FOLDER_SOUNDS . "/{$subFolder}";
        return $this->uploadFile($userId, $file, $folder, $isPublic, $isTemporary);
    }

    /**
     * 上传资源文件
     */
    public function uploadResourceFile(int $userId, UploadedFile $file, string $subFolder = self::SUBFOLDER_GENERATED, bool $isPublic = true, bool $isTemporary = false): array
    {
        $folder = self::FOLDER_RESOURCES . "/{$subFolder}";
        return $this->uploadFile($userId, $file, $folder, $isPublic, $isTemporary);
    }

    /**
     * 上传用户文件
     */
    public function uploadUserFile(int $userId, UploadedFile $file, string $subFolder = self::SUBFOLDER_UPLOADS, bool $isPublic = false, bool $isTemporary = false): array
    {
        $folder = self::FOLDER_USERS . "/{$userId}/{$subFolder}";
        return $this->uploadFile($userId, $file, $folder, $isPublic, $isTemporary);
    }

    /**
     * 上传临时文件
     */
    public function uploadTempFile(int $userId, UploadedFile $file, string $subFolder = self::SUBFOLDER_UPLOADS, bool $isPublic = false): array
    {
        $folder = self::FOLDER_TEMP . "/{$subFolder}";
        return $this->uploadFile($userId, $file, $folder, $isPublic, true);
    }

    /**
     * 获取预定义文件夹路径
     */
    public function getProjectFolderPath(int $projectId, string $subFolder = self::SUBFOLDER_IMAGES): string
    {
        return self::FOLDER_PROJECTS . "/{$projectId}/{$subFolder}";
    }

    public function getCharacterFolderPath(string $subFolder = self::SUBFOLDER_AVATARS): string
    {
        return self::FOLDER_CHARACTERS . "/{$subFolder}";
    }

    public function getStyleFolderPath(string $subFolder = self::SUBFOLDER_THUMBNAILS): string
    {
        return self::FOLDER_STYLES . "/{$subFolder}";
    }

    public function getVoiceFolderPath(string $subFolder = self::SUBFOLDER_SAMPLES): string
    {
        return self::FOLDER_VOICES . "/{$subFolder}";
    }

    public function getSoundFolderPath(string $subFolder = self::SUBFOLDER_GENERATED): string
    {
        return self::FOLDER_SOUNDS . "/{$subFolder}";
    }

    public function getResourceFolderPath(string $subFolder = self::SUBFOLDER_GENERATED): string
    {
        return self::FOLDER_RESOURCES . "/{$subFolder}";
    }

    public function getUserFolderPath(int $userId, string $subFolder = self::SUBFOLDER_UPLOADS): string
    {
        return self::FOLDER_USERS . "/{$userId}/{$subFolder}";
    }

    public function getTempFolderPath(string $subFolder = self::SUBFOLDER_UPLOADS): string
    {
        return self::FOLDER_TEMP . "/{$subFolder}";
    }
}
