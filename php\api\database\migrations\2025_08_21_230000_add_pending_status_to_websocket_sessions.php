<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 为WebSocket会话表添加pending状态
 * 修复认证时状态应该是pending，连接成功后才是connected的问题
 */
class AddPendingStatusToWebsocketSessions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('websocket_sessions', function (Blueprint $table) {
            // 修改status字段，添加pending状态
            $table->enum('status', ['pending', 'connected', 'disconnected', 'timeout'])
                  ->default('pending')
                  ->change()
                  ->comment('连接状态：pending=已认证待连接，connected=已连接，disconnected=已断开，timeout=超时');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('websocket_sessions', function (Blueprint $table) {
            // 恢复原来的状态
            $table->enum('status', ['connected', 'disconnected', 'timeout'])
                  ->default('connected')
                  ->change()
                  ->comment('连接状态');
        });
    }
}
