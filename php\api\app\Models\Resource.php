<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 资源模型
 * 第3A阶段：资源管理核心模型
 */
class Resource extends Model
{
    use SoftDeletes;

    protected $table = 'resources';

    // 状态常量 - 匹配数据库枚举值
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_READY = 'ready';
    const STATUS_AI_COMPLETED = 'ai_completed';

    // 资源类型常量
    const TYPE_STORY = 'story';
    const TYPE_IMAGE = 'image';
    const TYPE_VOICE = 'voice';
    const TYPE_VIDEO = 'video';
    const TYPE_MUSIC = 'music';
    const TYPE_SOUND = 'sound';

    // 质量级别常量
    const QUALITY_LOW = 'low';
    const QUALITY_MEDIUM = 'medium';
    const QUALITY_HIGH = 'high';
    const QUALITY_ULTRA = 'ultra';

    protected $fillable = [
        'resource_uuid',
        'user_id',
        'project_id',
        'resource_type',
        'status',
        'generation_config',
        'output_format',
        'quality_level',
        'batch_size',
        'estimated_cost',
        'actual_cost',
        'generation_task_id',
        'file_path',
        'file_size',
        'file_hash',
        'download_count',
        'processing_time_ms',
        'metadata',
        'completed_at'
    ];

    protected $casts = [
        'generation_config' => 'array',
        'metadata' => 'array',
        'estimated_cost' => 'decimal:4',
        'actual_cost' => 'decimal:4',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $dates = [
        'completed_at',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联项目
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * 关联生成任务（通过项目ID）
     */
    public function generationTasks()
    {
        return $this->hasMany(AiGenerationTask::class, 'project_id', 'project_id');
    }

    /**
     * 关联资源版本
     */
    public function versions()
    {
        return $this->hasMany(ResourceVersion::class);
    }

    /**
     * 关联当前版本
     */
    public function currentVersion()
    {
        return $this->belongsTo(ResourceVersion::class, 'current_version_id');
    }

    /**
     * 关联导出记录
     */
    public function exports()
    {
        return $this->hasMany(ResourceExport::class);
    }

    /**
     * 关联下载记录
     */
    public function downloads()
    {
        return $this->hasMany(ResourceDownload::class);
    }

    /**
     * 作用域：按用户过滤
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按项目过滤
     */
    public function scopeByProject($query, int $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    /**
     * 作用域：按资源类型过滤
     */
    public function scopeByType($query, string $resourceType)
    {
        return $query->where('resource_type', $resourceType);
    }

    /**
     * 作用域：按状态过滤
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：已完成的资源
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * 作用域：处理中的资源
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', self::STATUS_PROCESSING);
    }

    /**
     * 作用域：最近创建的资源
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * 检查资源是否已完成
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * 检查资源是否正在处理
     */
    public function isProcessing(): bool
    {
        return $this->status === self::STATUS_PROCESSING;
    }

    /**
     * 检查资源是否失败
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 检查资源是否可下载
     */
    public function isDownloadable(): bool
    {
        return $this->isCompleted() && !empty($this->file_path);
    }

    /**
     * 获取文件大小（格式化）
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'N/A';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($this->file_size, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . $units[$pow];
    }

    /**
     * 获取处理时间（格式化）
     */
    public function getFormattedProcessingTimeAttribute(): string
    {
        if (!$this->processing_time_ms) {
            return 'N/A';
        }

        $seconds = $this->processing_time_ms / 1000;
        
        if ($seconds < 60) {
            return round($seconds, 1) . 's';
        } elseif ($seconds < 3600) {
            return round($seconds / 60, 1) . 'm';
        } else {
            return round($seconds / 3600, 1) . 'h';
        }
    }

    /**
     * 获取状态显示名称
     */
    public function getStatusDisplayAttribute(): string
    {
        $statusMap = [
            self::STATUS_PENDING => '等待中',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败',
            self::STATUS_CANCELLED => '已取消'
        ];

        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取资源类型显示名称
     */
    public function getTypeDisplayAttribute(): string
    {
        $typeMap = [
            self::TYPE_STORY => '故事',
            self::TYPE_IMAGE => '图像',
            self::TYPE_VOICE => '语音',
            self::TYPE_VIDEO => '视频',
            self::TYPE_MUSIC => '音乐',
            self::TYPE_SOUND => '音效'
        ];

        return $typeMap[$this->resource_type] ?? '未知';
    }

    /**
     * 获取质量级别显示名称
     */
    public function getQualityDisplayAttribute(): string
    {
        $qualityMap = [
            self::QUALITY_LOW => '低质量',
            self::QUALITY_MEDIUM => '中等质量',
            self::QUALITY_HIGH => '高质量',
            self::QUALITY_ULTRA => '超高质量'
        ];

        return $qualityMap[$this->quality_level] ?? '未知';
    }

    /**
     * 增加下载次数
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    /**
     * 更新文件信息
     */
    public function updateFileInfo(string $filePath, int $fileSize, string $fileHash = null): void
    {
        $this->update([
            'file_path' => $filePath,
            'file_size' => $fileSize,
            'file_hash' => $fileHash
        ]);
    }

    /**
     * 标记为完成
     */
    public function markAsCompleted(float $actualCost = null, int $processingTimeMs = null): void
    {
        $updateData = [
            'status' => self::STATUS_COMPLETED,
            'completed_at' => Carbon::now()
        ];

        if ($actualCost !== null) {
            $updateData['actual_cost'] = $actualCost;
        }

        if ($processingTimeMs !== null) {
            $updateData['processing_time_ms'] = $processingTimeMs;
        }

        $this->update($updateData);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(string $errorMessage = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'metadata' => array_merge($this->metadata ?? [], [
                'error_message' => $errorMessage,
                'failed_at' => Carbon::now()->format('c')
            ])
        ]);
    }

    // ==================== 文件路径管理方法 ====================

    /**
     * 获取资源文件夹路径
     */
    public function getResourceFolderPath(string $subFolder = 'generated'): string
    {
        return "resources/{$subFolder}";
    }

    /**
     * 根据资源类型获取对应的文件夹路径
     */
    public function getTypedFolderPath(): string
    {
        $subFolder = match($this->resource_type) {
            self::TYPE_IMAGE => 'generated/images',
            self::TYPE_VIDEO => 'generated/videos',
            self::TYPE_VOICE => 'generated/audio',
            self::TYPE_MUSIC => 'generated/music',
            self::TYPE_SOUND => 'generated/sounds',
            self::TYPE_STORY => 'generated/stories',
            default => 'generated'
        };

        return "resources/{$subFolder}";
    }

    /**
     * 获取导出文件夹路径
     */
    public function getExportsFolderPath(): string
    {
        return $this->getResourceFolderPath('exports');
    }

    /**
     * 获取缓存文件夹路径
     */
    public function getCacheFolderPath(): string
    {
        return $this->getResourceFolderPath('cache');
    }

    /**
     * 生成资源文件名
     */
    public function generateFileName(string $extension = 'bin'): string
    {
        return "resource_{$this->resource_uuid}.{$extension}";
    }

    /**
     * 获取完整文件路径
     */
    public function getFullFilePath(): string
    {
        if ($this->file_path) {
            return $this->file_path;
        }

        $folderPath = $this->getTypedFolderPath();
        $fileName = $this->generateFileName();

        return "{$folderPath}/{$fileName}";
    }
}
