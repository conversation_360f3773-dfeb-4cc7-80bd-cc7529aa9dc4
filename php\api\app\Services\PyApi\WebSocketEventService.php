<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Helpers\EventTypeHelper;
use App\Enums\ApiCodeEnum;
use App\Models\WebSocketSession;
use Carbon\Carbon;
use App\Models\AiGenerationTask;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * WebSocket事件推送服务
 */
class WebSocketEventService
{
    protected $webSocketService;
    protected $pointsService;

    // 重试配置
    const MAX_RETRY_ATTEMPTS = 3;
    const RETRY_DELAY_SECONDS = 1;
    const EXPONENTIAL_BACKOFF_MULTIPLIER = 2;

    // 降级处理配置
    const FALLBACK_CACHE_TTL = 300; // 5分钟
    const CIRCUIT_BREAKER_THRESHOLD = 5; // 连续失败5次后开启断路器
    const CIRCUIT_BREAKER_TIMEOUT = 60; // 断路器开启60秒后重试

    public function __construct(WebSocketService $webSocketService, PointsService $pointsService)
    {
        $this->webSocketService = $webSocketService;
        $this->pointsService = $pointsService;
    }

    /**
     * 推送AI生成进度事件
     */
    public function pushAiGenerationProgress(string $taskId, int $userId, int $progress, string $message = '', ?string $taskType = null): array
    {
        try {
            // 检查断路器状态
            $circuitBreakerResult = $this->isCircuitBreakerOpen($userId);
            if ($circuitBreakerResult['data']['is_open']) {
                Log::warning('WebSocket断路器开启，跳过推送', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'progress' => $progress
                ]);

                // 直接存储到降级缓存
                $eventData = [
                    'task_id' => $taskId,
                    'progress' => $progress,
                    'message' => $message,
                    'timestamp' => Carbon::now()->format('c')
                ];
                $this->storeFallbackMessage($userId, WebSocketSession::EVENT_AI_GENERATION_PROGRESS, $eventData);

                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => 'WebSocket断路器开启，推送已降级处理',
                    'data' => []
                ];
            }

            // 🔧 优化：如果传入了taskType，优先使用，避免数据库查询
            // 这样可以解决任务记录还未创建时的进度推送问题
            $task = null;
            $resolvedTaskType = $taskType;
            $platform = 'unknown';
            $modelName = 'unknown';

            if (!$taskType) {
                // 只有在没有传入taskType时才查询数据库
                $task = AiGenerationTask::where('external_task_id', $taskId)->first();
                if (!$task) {
                    Log::error('未找到任务记录且未传入taskType - 需要排查任务创建失败原因', [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                        'progress' => $progress,
                        'message' => $message,
                        'method' => __METHOD__,
                        'line' => __LINE__,
                        'task_type_provided' => false
                    ]);

                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '未找到任务记录且未传入taskType',
                        'data' => []
                    ];
                }
                $resolvedTaskType = $task->task_type;
                $platform = $task->platform;
                $modelName = $task->model_name;
            } else {
                Log::info('进度推送使用传入的taskType，跳过数据库查询', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'task_type' => $taskType,
                    'progress' => $progress,
                    'method' => __METHOD__
                ]);

                // 当没有任务记录时，使用默认值
                if ($task) {
                    $platform = $task->platform;
                    $modelName = $task->model_name;
                }
            }

            $eventData = [
                'task_id' => $taskId,
                'task_type' => $resolvedTaskType, // 🔧 使用resolvedTaskType
                'platform' => $platform,         // 🔧 使用解析后的platform
                'model_name' => $modelName,       // 🔧 使用解析后的modelName
                'progress' => $progress,
                'message' => $message,
                'timestamp' => Carbon::now('Asia/Shanghai')->format('c')
            ];

            // 使用带重试机制的推送
            $retryResult = $this->pushWithRetry(
                $userId,
                WebSocketSession::EVENT_AI_GENERATION_PROGRESS,
                $eventData,
                "ai_generation_progress:{$taskId}"
            );

            if ($retryResult['code'] === ApiCodeEnum::SUCCESS) {
                Log::info('AI生成进度推送成功', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'progress' => $progress,
                    'message' => $message
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'AI生成进度推送成功',
                    'data' => [
                        'task_id' => $taskId,
                        'progress' => $progress,
                        'message' => $message
                    ]
                ];
            } else {
                return [
                    'code' => ApiCodeEnum::ERROR,
                    'message' => 'AI生成进度推送失败：' . $retryResult['message'],
                    'data' => []
                ];
            }

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
                'progress' => $progress,
                'message' => $message,
            ];

            Log::error('AI生成进度推送异常', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 异常情况下也尝试存储降级消息
            try {
                $eventData = [
                    'task_id' => $taskId,
                    'progress' => $progress,
                    'message' => $message,
                    'timestamp' => Carbon::now()->format('c')
                ];
                $this->storeFallbackMessage($userId, WebSocketSession::EVENT_AI_GENERATION_PROGRESS, $eventData);
            } catch (\Exception $fallbackException) {
                Log::error('降级消息存储也失败', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'error' => $fallbackException->getMessage()
                ]);
            }

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => 'AI生成进度推送异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 推送AI生成完成事件（支持成功和失败两种结果）
     *
     * 🎯 核心作用：WebSocket实时通知前端用户 + 处理积分消费/返还
     *
     * @param string $taskId 任务ID
     * @param int $userId 用户ID
     * @param string|null $taskType 任务类型（可选，避免数据库查询）
     * @param string|null $errorMessage 错误消息（失败时传入）
     * @return array
     *
     * 🎭 前端处理统一机制：
     * - 监听 *_completed 事件 → 显示成功结果，隐藏进度条
     * - 监听 *_failed 事件 → 显示失败信息，隐藏进度条
     * - 积分变化实时反映到用户界面
     */
    public function pushAiGenerationCompleted(string $taskId, int $userId, ?string $taskType = null, ?string $errorMessage = null): array
    {
        try {
            // 🔧 优化：先检查断路器状态，与 pushAiGenerationProgress 保持一致
            $circuitBreakerResult = $this->isCircuitBreakerOpen($userId);
            if ($circuitBreakerResult['data']['is_open']) {
                Log::warning('WebSocket断路器开启，跳过推送', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'event_type' => 'ai_generation_completed'
                ]);

                // 直接存储到降级缓存
                $eventData = [
                    'task_id' => $taskId,
                    'status' => 'completed',
                    'timestamp' => Carbon::now('Asia/Shanghai')->format('c')
                ];
                $this->storeFallbackMessage($userId, 'ai_generation_completed', $eventData);

                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => 'WebSocket断路器开启，推送已降级处理',
                    'data' => []
                ];
            }

            // 🔧 优化：如果传入了taskType，直接使用，避免数据库查询
            // 这样可以解决事务回滚场景下任务记录被删除的问题
            $task = null;
            $resolvedTaskType = $taskType;

            if (!$taskType) {
                // 只有在没有传入taskType时才查询数据库
                $task = AiGenerationTask::where('external_task_id', $taskId)->first();
                if (!$task) {
                    Log::error('未找到任务记录且未传入taskType - 需要排查任务创建失败原因', [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                        'method' => __METHOD__,
                        'line' => __LINE__,
                        'task_type_provided' => false
                    ]);

                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '未找到任务记录且未传入taskType',
                        'data' => []
                    ];
                }
                $resolvedTaskType = $task->task_type;
            } else {
                Log::info('使用传入的taskType，跳过数据库查询', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'task_type' => $taskType,
                    'method' => __METHOD__
                ]);
            }

            // 🔧 根据是否有错误消息决定事件类型和数据
            $isFailure = !empty($errorMessage);

            if ($isFailure) {
                // 🔔 失败情况：获取WebSocket事件类型名称
                // 💡 为什么使用 ProcessTextGeneration::publishFailureEvent 相同的 EventTypeHelper::getFailedEventType()
                // - 保证WebSocket事件和事件总线事件的类型名称一致
                // - 便于前后端系统根据统一的事件类型进行处理
                // 🔄 流程的特点：
                // 1. 只有WebSocket事件通知前端用户
                // 2. 不发布到事件总线（成功是正常情况，无需系统监控）
                // 3. 消费积分完成交易
                //
                // 💡 设计理念：
                // - 成功：用户需要知道，系统无需特别关注
                // - 失败：用户需要知道，系统也需要监控分析
                $eventType = EventTypeHelper::getFailedEventType($resolvedTaskType)
                    ?? 'ai_generation_failed';

                // 🔧 根据是否有任务记录决定数据结构
                if ($task) {
                    // 有任务记录，使用完整数据
                    $eventData = [
                        'task_id' => $taskId,
                        'task_type' => $task->task_type,
                        'platform' => $task->platform,
                        'model_name' => $task->model_name,
                        'status' => 'failed',
                        'error_message' => $errorMessage,
                        'retry_count' => $task->retry_count,
                        'can_retry' => $task->canRetry(),
                        'failed_at' => $task->completed_at?->format('c'),
                        'timestamp' => Carbon::now('Asia/Shanghai')->format('c')
                    ];
                } else {
                    // 没有任务记录（事务回滚场景），使用基础数据
                    $eventData = [
                        'task_id' => $taskId,
                        'task_type' => $resolvedTaskType,
                        'platform' => 'unknown', // 无法获取，使用默认值
                        'model_name' => 'unknown', // 无法获取，使用默认值
                        'status' => 'failed',
                        'error_message' => $errorMessage,
                        'retry_count' => 0, // 无法获取，使用默认值
                        'can_retry' => false, // 无法获取，使用默认值
                        'failed_at' => Carbon::now('Asia/Shanghai')->format('c'),
                        'timestamp' => Carbon::now('Asia/Shanghai')->format('c')
                    ];
                }
            } else {
                // ✅ 成功情况：构建WebSocket完成事件类型
                // 💡 为什么使用 ProcessTextGeneration::publishFailureEvent 相同的 EventTypeHelper::getFailedEventType()
                // - 保证WebSocket事件和事件总线事件的类型名称一致
                // - 便于前后端系统根据统一的事件类型进行处理
                // 🔄 流程的特点：
                // 1. 只有WebSocket事件通知前端用户
                // 2. 不发布到事件总线（成功是正常情况，无需系统监控）
                // 3. 消费积分完成交易
                //
                // 💡 设计理念：
                // - 成功：用户需要知道，系统无需特别关注
                // - 失败：用户需要知道，系统也需要监控分析
                $eventType = EventTypeHelper::getCompletedEventType($resolvedTaskType)
                    ?? 'ai_generation_completed';

                // 成功情况必须有任务记录
                if (!$task) {
                    Log::error('成功情况下必须有任务记录', [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                        'task_type' => $resolvedTaskType
                    ]);
                    return [
                        'code' => ApiCodeEnum::ERROR,
                        'message' => '成功情况下必须有任务记录',
                        'data' => []
                    ];
                }

                $eventData = [
                    'task_id' => $taskId,
                    'task_type' => $task->task_type,
                    'platform' => $task->platform,
                    'model_name' => $task->model_name,
                    'status' => $task->status,
                    'output_data' => $task->output_data,
                    'cost' => $task->cost,
                    'tokens_used' => $task->tokens_used,
                    'processing_time_ms' => $task->processing_time_ms,
                    'completed_at' => $task->completed_at?->format('c'),
                    'timestamp' => Carbon::now('Asia/Shanghai')->format('c')
                ];
            }

            // 写日志
            Log::info('WebSocket推送事件 - 任务完成', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'task_type' => $resolvedTaskType, // 🔧 使用resolvedTaskType而不是$task->task_type
                'push_type' => $eventType,
                'redis_channel' => "websocket:push:session_*",
                'push_method' => 'Redis通道桥接',
                'timestamp' => time(),
                'job_class' => self::class,
                'job_method' => 'handle'
            ]);

            // 使用带重试机制的推送
            $retryResult = $this->pushWithRetry(
                $userId,
                $eventType,
                $eventData,
                "{$eventType}:{$taskId}"
            );

            if ($retryResult['code'] === ApiCodeEnum::SUCCESS) {
                $statusText = $isFailure ? '失败' : '完成';
                Log::info("AI生成{$statusText}推送成功", [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'task_type' => $resolvedTaskType, // 🔧 使用resolvedTaskType
                    'is_failure' => $isFailure,
                    'event_type' => $eventType
                ]);

                // 🎯 处理积分消费或返还
                $pointsResult = $this->handlePointsTransaction($task, $isFailure, $taskId, $userId);
                if ($pointsResult['code'] !== ApiCodeEnum::SUCCESS) {
                    Log::warning("积分处理失败，但WebSocket推送已成功", [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                        'points_error' => $pointsResult['message'],
                        'is_failure' => $isFailure
                    ]);
                }

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => "AI生成{$statusText}推送成功",
                    'data' => [
                        'task_id' => $taskId,
                        'task_type' => $resolvedTaskType, // 🔧 使用resolvedTaskType
                        'status' => $isFailure ? 'failed' : ($task ? $task->status : 'unknown'), // 🔧 安全访问
                        'event_type' => $eventType,
                        'points_handled' => $pointsResult['code'] === ApiCodeEnum::SUCCESS
                    ]
                ];
            } else {
                $statusText = $isFailure ? '失败' : '完成';
                return [
                    'code' => ApiCodeEnum::ERROR,
                    'message' => "AI生成{$statusText}推送失败：" . $retryResult['message'],
                    'data' => []
                ];
            }

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('AI生成完成推送异常', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 异常情况下也尝试存储降级消息
            try {
                $eventData = [
                    'task_id' => $taskId,
                    'status' => 'completed',
                    'timestamp' => Carbon::now()->format('c')
                ];
                $this->storeFallbackMessage($userId, $eventType, $eventData);
            } catch (\Exception $fallbackException) {
                Log::error('降级消息存储也失败', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'error' => $fallbackException->getMessage()
                ]);
            }

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => 'AI生成完成推送异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 处理积分交易（消费或返还）
     *
     * @param AiGenerationTask|null $task 任务对象
     * @param bool $isFailure 是否失败
     * @param string $taskId 任务ID
     * @param int $userId 用户ID
     * @return array
     */
    private function handlePointsTransaction($task, bool $isFailure, string $taskId, int $userId): array
    {
        try {
            // 如果没有任务记录，无法处理积分
            if (!$task) {
                Log::warning('无任务记录，跳过积分处理', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'is_failure' => $isFailure
                ]);
                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '无任务记录，跳过积分处理',
                    'data' => []
                ];
            }

            // 如果没有交易ID，无法处理积分
            if (!$task->transaction_id) {
                Log::warning('任务缺少transaction_id，跳过积分处理', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'is_failure' => $isFailure
                ]);
                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '任务缺少transaction_id，跳过积分处理',
                    'data' => []
                ];
            }

            if ($isFailure) {
                // 失败情况：释放冻结积分
                Log::info('任务失败，释放冻结积分', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'transaction_id' => $task->transaction_id
                ]);

                $result = $this->pointsService->releasePoints($task->transaction_id);

                if ($result['code'] === ApiCodeEnum::SUCCESS) {
                    Log::info('积分释放成功', [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                        'transaction_id' => $task->transaction_id,
                        'amount' => $result['data']['amount'] ?? 0
                    ]);
                } else {
                    Log::error('积分释放失败', [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                        'transaction_id' => $task->transaction_id,
                        'error' => $result['message']
                    ]);
                }

                return $result;
            } else {
                // 成功情况：消费冻结积分
                Log::info('任务成功，消费冻结积分', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'transaction_id' => $task->transaction_id
                ]);

                // 🎯 关键：$task->transaction_id 来源链路
                // 1. generateTextWithWebSocket() 冻结积分时获取：$transactionId = $freezeResult['data']['transaction_id']
                // 2. 创建任务记录时存储：'transaction_id' => $transactionId
                // 3. 消费积分时使用：$task->transaction_id（从p_ai_generation_tasks表读取）
                $result = $this->pointsService->consumePoints($task->transaction_id);

                if ($result['code'] === ApiCodeEnum::SUCCESS) {
                    Log::info('积分消费成功', [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                        'transaction_id' => $task->transaction_id,
                        'amount' => $result['data']['amount'] ?? 0
                    ]);
                } else {
                    Log::error('积分消费失败', [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                        'transaction_id' => $task->transaction_id,
                        'error' => $result['message']
                    ]);
                }

                return $result;
            }

        } catch (\Exception $e) {
            Log::error('积分处理异常', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'is_failure' => $isFailure,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '积分处理异常: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 推送AI生成失败事件
     */
    public function pushAiGenerationFailed(string $taskId, int $userId, string $errorMessage): array
    {
        try {
            // 通过external_task_id查找任务记录
            $task = AiGenerationTask::where('external_task_id', $taskId)->first();
            if (!$task) {
                Log::warning('未找到任务记录', [
                    'task_id' => $taskId,
                    'user_id' => $userId
                ]);

                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '未找到任务记录',
                    'data' => []
                ];
            }

            // 根据任务类型获取对应的失败事件类型
            $eventType = EventTypeHelper::getFailedEventType($task->task_type)
                ?? 'ai_generation_failed';

            $eventData = [
                'task_id' => $taskId,
                'task_type' => $task->task_type,
                'platform' => $task->platform,
                'model_name' => $task->model_name,
                'status' => $task->status,
                'error_message' => $errorMessage,
                'retry_count' => $task->retry_count,
                'can_retry' => $task->canRetry(),
                'failed_at' => $task->completed_at?->format('c'),
                'timestamp' => Carbon::now()->format('c')
            ];

            $pushResult = $this->webSocketService->pushToUser(
                $userId,
                $eventType,
                $eventData
            );

            Log::info('AI生成失败推送', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error_message' => $errorMessage,
                'push_result' => $pushResult
            ]);

            if ($pushResult['code'] === ApiCodeEnum::SUCCESS) {
                // 🎯 处理积分返还（最终失败时）
                $pointsResult = $this->handlePointsTransaction($task, true, $taskId, $userId);
                if ($pointsResult['code'] !== ApiCodeEnum::SUCCESS) {
                    Log::warning("积分返还失败，但WebSocket推送已成功", [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                        'points_error' => $pointsResult['message'],
                        'context' => 'final_failure'
                    ]);
                }

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'AI生成失败推送成功',
                    'data' => [
                        'task_id' => $taskId,
                        'error_message' => $errorMessage,
                        'push_count' => $pushResult['data']['success_count'] ?? 0,
                        'points_handled' => $pointsResult['code'] === ApiCodeEnum::SUCCESS
                    ]
                ];
            } else {
                return [
                    'code' => ApiCodeEnum::ERROR,
                    'message' => 'AI生成失败推送失败：' . $pushResult['message'],
                    'data' => []
                ];
            }

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error_message' => $errorMessage,
            ];

            Log::error('AI生成失败推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => 'AI生成失败推送异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 推送积分变动事件
     */
    public function pushPointsChanged(int $userId, string $changeType, float $amount, float $newBalance, string $reason = ''): array
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '用户不存在',
                    'data' => []
                ];
            }

            $eventData = [
                'user_id' => $userId,
                'change_type' => $changeType, // 'increase', 'decrease', 'freeze', 'release'
                'amount' => $amount,
                'new_balance' => $newBalance,
                'frozen_points' => $user->frozen_points,
                'total_points' => $newBalance + $user->frozen_points,
                'reason' => $reason,
                'timestamp' => Carbon::now()->format('c')
            ];

            $successCount = $this->webSocketService->pushToUser(
                $userId,
                WebSocketSession::EVENT_POINTS_CHANGED,
                $eventData
            );

            Log::info('积分变动推送', [
                'user_id' => $userId,
                'change_type' => $changeType,
                'amount' => $amount,
                'new_balance' => $newBalance,
                'success_count' => $successCount
            ]);

            if ($successCount > 0) {
                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '积分变动推送成功',
                    'data' => [
                        'user_id' => $userId,
                        'change_type' => $changeType,
                        'amount' => $amount,
                        'new_balance' => $newBalance,
                        'success_count' => $successCount
                    ]
                ];
            } else {
                return [
                    'code' => ApiCodeEnum::ERROR,
                    'message' => '积分变动推送失败，没有成功的会话',
                    'data' => []
                ];
            }

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'change_type' => $changeType,
                'amount' => $amount,
                'new_balance' => $newBalance,
                'reason' => $reason,
            ];

            Log::error('积分变动推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '积分变动推送异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 推送自定义事件
     */
    public function pushCustomEvent(int $userId, string $eventType, array $eventData): array
    {
        try {
            $data = array_merge($eventData, [
                'timestamp' => Carbon::now()->format('c')
            ]);

            $pushResult = $this->webSocketService->pushToUser($userId, $eventType, $data);

            if ($pushResult['code'] === ApiCodeEnum::SUCCESS) {
                $successCount = $pushResult['data']['success_count'] ?? 0;

                Log::info('自定义事件推送', [
                    'user_id' => $userId,
                    'event_type' => $eventType,
                    'success_count' => $successCount
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '自定义事件推送成功',
                    'data' => [
                        'user_id' => $userId,
                        'event_type' => $eventType,
                        'success_count' => $successCount
                    ]
                ];
            } else {
                return [
                    'code' => ApiCodeEnum::ERROR,
                    'message' => '自定义事件推送失败：' . $pushResult['message'],
                    'data' => []
                ];
            }

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'event_type' => $eventType,
                'event_data' => $eventData,
            ];

            Log::error('自定义事件推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '自定义事件推送异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 推送系统通知事件
     */
    public function pushSystemNotification(int $userId, string $title, string $message, string $type = 'info'): array
    {
        try {
            $eventData = [
                'title' => $title,
                'message' => $message,
                'type' => $type, // 'info', 'success', 'warning', 'error'
                'timestamp' => Carbon::now()->format('c')
            ];

            $pushResult = $this->webSocketService->pushToUser($userId, 'system_notification', $eventData);

            if ($pushResult['code'] === ApiCodeEnum::SUCCESS) {
                $successCount = $pushResult['data']['success_count'] ?? 0;

                Log::info('系统通知推送', [
                    'user_id' => $userId,
                    'title' => $title,
                    'type' => $type,
                    'success_count' => $successCount
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '系统通知推送成功',
                    'data' => [
                        'user_id' => $userId,
                        'title' => $title,
                        'type' => $type,
                        'success_count' => $successCount
                    ]
                ];
            } else {
                return [
                    'code' => ApiCodeEnum::ERROR,
                    'message' => '系统通知推送失败：' . $pushResult['message'],
                    'data' => []
                ];
            }

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'title' => $title,
                'message' => $message,
                'type' => $type,
            ];

            Log::error('系统通知推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '系统通知推送异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 批量推送事件到多个用户
     */
    public function pushToMultipleUsers(array $userIds, string $eventType, array $eventData): array
    {
        try {
            $results = [];

            foreach ($userIds as $userId) {
                $results[$userId] = $this->webSocketService->pushToUser($userId, $eventType, $eventData) > 0;
            }

            $successCount = count(array_filter($results));

            Log::info('批量事件推送', [
                'user_count' => count($userIds),
                'event_type' => $eventType,
                'success_count' => $successCount
            ]);

            return $results;

        } catch (\Exception $e) {
            $error_context = [
                'user_ids' => $userIds,
                'event_type' => $eventType,
                'event_data' => $eventData,
            ];

            Log::error('批量事件推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [];
        }
    }

    /**
     * 推送广播事件到所有活跃连接
     */
    public function pushBroadcast(string $eventType, array $eventData): array
    {
        try {
            $activeSessions = WebSocketSession::active()->get();
            $successCount = 0;

            foreach ($activeSessions as $session) {
                $pushResult = $this->webSocketService->pushMessage($session->session_id, $eventType, $eventData);
                if ($pushResult['code'] === ApiCodeEnum::SUCCESS) {
                    $successCount++;
                }
            }

            Log::info('广播事件推送', [
                'event_type' => $eventType,
                'total_sessions' => $activeSessions->count(),
                'success_count' => $successCount
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '广播事件推送完成',
                'data' => [
                    'event_type' => $eventType,
                    'total_sessions' => $activeSessions->count(),
                    'success_count' => $successCount,
                    'success_rate' => $activeSessions->count() > 0 ? round($successCount / $activeSessions->count() * 100, 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'event_type' => $eventType,
                'event_data' => $eventData,
            ];

            Log::error('广播事件推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '广播事件推送异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 带重试机制的推送方法
     */
    protected function pushWithRetry(int $userId, string $eventType, array $eventData, string $context = ''): array
    {
        $startTime = microtime(true);
        $attempts = 0;
        $lastException = null;
        $pushId = uniqid('push_');

        // 记录推送开始日志
        Log::info('WebSocket推送开始', [
            'push_id' => $pushId,
            'user_id' => $userId,
            'event_type' => $eventType,
            'context' => $context,
            'data_size' => strlen(json_encode($eventData)),
            'max_attempts' => self::MAX_RETRY_ATTEMPTS,
            'timestamp' => Carbon::now()->format('c')
        ]);

        while ($attempts < self::MAX_RETRY_ATTEMPTS) {
            $attemptStartTime = microtime(true);

            try {
                $pushResult = $this->webSocketService->pushToUser($userId, $eventType, $eventData);
                $attemptDuration = round((microtime(true) - $attemptStartTime) * 1000, 2);

                if ($pushResult['code'] === ApiCodeEnum::SUCCESS) {
                    $successCount = $pushResult['data']['success_count'] ?? 0;

                    // 推送成功，重置断路器
                    $resetResult = $this->resetCircuitBreaker($userId);

                    $totalDuration = round((microtime(true) - $startTime) * 1000, 2);
                    Log::info('WebSocket推送成功', [
                        'push_id' => $pushId,
                        'user_id' => $userId,
                        'event_type' => $eventType,
                        'context' => $context,
                        'attempt' => $attempts + 1,
                        'success_count' => $successCount,
                        'attempt_duration_ms' => $attemptDuration,
                        'total_duration_ms' => $totalDuration,
                        'timestamp' => Carbon::now()->format('c')
                    ]);

                    return [
                        'code' => ApiCodeEnum::SUCCESS,
                        'message' => 'WebSocket推送重试成功',
                        'data' => [
                            'push_id' => $pushId,
                            'user_id' => $userId,
                            'event_type' => $eventType,
                            'context' => $context,
                            'attempts' => $attempts + 1,
                            'success_count' => $successCount,
                            'total_duration_ms' => $totalDuration
                        ]
                    ];
                }

                // 推送失败但没有异常，记录并重试
                Log::warning('WebSocket推送失败，准备重试', [
                    'push_id' => $pushId,
                    'user_id' => $userId,
                    'event_type' => $eventType,
                    'context' => $context,
                    'attempt' => $attempts + 1,
                    'success_count' => 0,
                    'attempt_duration_ms' => $attemptDuration,
                    'reason' => 'push_failed: ' . ($pushResult['message'] ?? 'unknown'),
                    'timestamp' => Carbon::now()->format('c')
                ]);

            } catch (\Exception $e) {
                $lastException = $e;
                $attemptDuration = round((microtime(true) - $attemptStartTime) * 1000, 2);

                Log::warning('WebSocket推送异常，准备重试', [
                    'push_id' => $pushId,
                    'user_id' => $userId,
                    'event_type' => $eventType,
                    'context' => $context,
                    'attempt' => $attempts + 1,
                    'attempt_duration_ms' => $attemptDuration,
                    'error' => $e->getMessage(),
                    'error_code' => $e->getCode(),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine(),
                    'timestamp' => Carbon::now()->format('c')
                ]);
            }

            $attempts++;

            // 如果不是最后一次尝试，等待后重试
            if ($attempts < self::MAX_RETRY_ATTEMPTS) {
                $delay = self::RETRY_DELAY_SECONDS * pow(self::EXPONENTIAL_BACKOFF_MULTIPLIER, $attempts - 1);

                Log::info('WebSocket推送重试延迟', [
                    'push_id' => $pushId,
                    'user_id' => $userId,
                    'event_type' => $eventType,
                    'next_attempt' => $attempts + 1,
                    'delay_seconds' => $delay,
                    'timestamp' => Carbon::now()->format('c')
                ]);

                sleep($delay);
            }
        }

        // 所有重试都失败了
        $totalDuration = round((microtime(true) - $startTime) * 1000, 2);
        Log::error('WebSocket推送最终失败', [
            'push_id' => $pushId,
            'user_id' => $userId,
            'event_type' => $eventType,
            'context' => $context,
            'total_attempts' => $attempts,
            'total_duration_ms' => $totalDuration,
            'last_error' => $lastException ? $lastException->getMessage() : 'Unknown error',
            'timestamp' => Carbon::now()->format('c')
        ]);

        $handleResult = $this->handlePushFailure($userId, $eventType, $eventData, $lastException, $context);

        return [
            'code' => ApiCodeEnum::ERROR,
            'message' => 'WebSocket推送重试失败，已达到最大重试次数',
            'data' => [
                'push_id' => $pushId,
                'user_id' => $userId,
                'event_type' => $eventType,
                'context' => $context,
                'max_attempts' => self::MAX_RETRY_ATTEMPTS,
                'last_exception' => $lastException ? $lastException->getMessage() : null,
                'fallback_handled' => $handleResult['code'] === ApiCodeEnum::SUCCESS
            ]
        ];
    }

    /**
     * 处理推送失败的降级策略
     */
    protected function handlePushFailure(int $userId, string $eventType, array $eventData, ?\Exception $exception, string $context): array
    {
        $failureId = uniqid('failure_');

        // 记录失败次数
        $failureCount = $this->incrementFailureCount($userId);

        // 检查是否需要开启断路器
        $circuitBreakerOpened = false;
        $shouldOpenResult = $this->shouldOpenCircuitBreaker($userId);
        if ($shouldOpenResult['data']['should_open']) {
            $this->openCircuitBreaker($userId);
            $circuitBreakerOpened = true;

            Log::error('WebSocket断路器开启', [
                'failure_id' => $failureId,
                'user_id' => $userId,
                'event_type' => $eventType,
                'context' => $context,
                'failure_count' => $failureCount,
                'threshold' => self::CIRCUIT_BREAKER_THRESHOLD,
                'timeout_seconds' => self::CIRCUIT_BREAKER_TIMEOUT,
                'timestamp' => Carbon::now()->format('c')
            ]);
        }

        // 降级处理：将消息存储到缓存中，供客户端轮询获取
        $fallbackStored = $this->storeFallbackMessage($userId, $eventType, $eventData);

        // 记录详细的失败处理日志
        Log::error('WebSocket推送失败处理完成', [
            'failure_id' => $failureId,
            'user_id' => $userId,
            'event_type' => $eventType,
            'context' => $context,
            'max_attempts' => self::MAX_RETRY_ATTEMPTS,
            'failure_count' => $failureCount,
            'circuit_breaker_opened' => $circuitBreakerOpened,
            'fallback_stored' => $fallbackStored['code'] === ApiCodeEnum::SUCCESS,
            'data_size' => strlen(json_encode($eventData)),
            'error_message' => $exception ? $exception->getMessage() : 'Unknown error',
            'error_type' => $exception ? get_class($exception) : 'Unknown',
            'timestamp' => Carbon::now()->format('c')
        ]);

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'WebSocket推送失败处理完成',
            'data' => [
                'failure_id' => $failureId,
                'user_id' => $userId,
                'event_type' => $eventType,
                'failure_count' => $failureCount['data']['new_count'] ?? $failureCount,
                'circuit_breaker_opened' => $circuitBreakerOpened,
                'fallback_stored' => $fallbackStored['code'] === ApiCodeEnum::SUCCESS
            ]
        ];
    }

    /**
     * 存储降级消息到缓存
     */
    protected function storeFallbackMessage(int $userId, string $eventType, array $eventData): array
    {
        $storageId = uniqid('storage_');

        try {
            $cacheKey = "websocket_fallback:{$userId}";
            $message = [
                'event_type' => $eventType,
                'data' => $eventData,
                'timestamp' => Carbon::now()->format('c'),
                'id' => uniqid('msg_'),
                'storage_id' => $storageId
            ];

            // 获取现有消息列表
            $existingMessages = Cache::get($cacheKey, []);
            $originalCount = count($existingMessages);

            $existingMessages[] = $message;

            // 限制消息数量，只保留最新的50条
            $trimmed = false;
            if (count($existingMessages) > 50) {
                $existingMessages = array_slice($existingMessages, -50);
                $trimmed = true;
            }

            // 存储到缓存
            $cacheStored = Cache::put($cacheKey, $existingMessages, self::FALLBACK_CACHE_TTL);

            Log::info('降级消息存储成功', [
                'storage_id' => $storageId,
                'user_id' => $userId,
                'event_type' => $eventType,
                'message_id' => $message['id'],
                'original_count' => $originalCount,
                'new_count' => count($existingMessages),
                'trimmed' => $trimmed,
                'cache_stored' => $cacheStored,
                'cache_ttl' => self::FALLBACK_CACHE_TTL,
                'data_size' => strlen(json_encode($eventData)),
                'timestamp' => Carbon::now()->format('c')
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '降级消息存储成功',
                'data' => [
                    'storage_id' => $storageId,
                    'user_id' => $userId,
                    'event_type' => $eventType
                ]
            ];

        } catch (\Exception $e) {
            Log::error('降级消息存储失败', [
                'storage_id' => $storageId,
                'user_id' => $userId,
                'event_type' => $eventType,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'data_size' => strlen(json_encode($eventData)),
                'timestamp' => Carbon::now()->format('c')
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '降级消息存储失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 检查断路器状态
     */
    protected function isCircuitBreakerOpen(int $userId): array
    {
        $cacheKey = "websocket_circuit_breaker:{$userId}";
        $breakerData = Cache::get($cacheKey);

        if (!$breakerData) {
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '断路器未开启',
                'data' => [
                    'is_open' => false,
                    'user_id' => $userId
                ]
            ];
        }

        // 检查断路器是否已过期
        if (Carbon::now()->timestamp > $breakerData['expires_at']) {
            Cache::forget($cacheKey);
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '断路器已过期，自动关闭',
                'data' => [
                    'is_open' => false,
                    'user_id' => $userId,
                    'was_expired' => true
                ]
            ];
        }

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => '断路器已开启',
            'data' => [
                'is_open' => true,
                'user_id' => $userId,
                'opened_at' => $breakerData['opened_at_iso'] ?? null,
                'expires_at' => $breakerData['expires_at_iso'] ?? null
            ]
        ];
    }

    /**
     * 开启断路器
     */
    protected function openCircuitBreaker(int $userId): array
    {
        $cacheKey = "websocket_circuit_breaker:{$userId}";
        $openedAt = Carbon::now();
        $expiresAt = $openedAt->copy()->addSeconds(self::CIRCUIT_BREAKER_TIMEOUT);

        $breakerData = [
            'opened_at' => $openedAt->timestamp,
            'expires_at' => $expiresAt->timestamp,
            'opened_at_iso' => $openedAt->format('c'),
            'expires_at_iso' => $expiresAt->format('c')
        ];

        $cacheStored = Cache::put($cacheKey, $breakerData, self::CIRCUIT_BREAKER_TIMEOUT + 60);

        Log::warning('WebSocket断路器已开启', [
            'user_id' => $userId,
            'opened_at' => $openedAt->format('c'),
            'expires_at' => $expiresAt->format('c'),
            'timeout_seconds' => self::CIRCUIT_BREAKER_TIMEOUT,
            'cache_stored' => $cacheStored,
            'timestamp' => Carbon::now()->format('c')
        ]);

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'WebSocket断路器已开启',
            'data' => [
                'user_id' => $userId,
                'opened_at' => $openedAt->format('c'),
                'expires_at' => $expiresAt->format('c'),
                'timeout_seconds' => self::CIRCUIT_BREAKER_TIMEOUT
            ]
        ];
    }

    /**
     * 重置断路器
     */
    protected function resetCircuitBreaker(int $userId): array
    {
        $circuitBreakerKey = "websocket_circuit_breaker:{$userId}";
        $failureCountKey = "websocket_failure_count:{$userId}";

        $hadCircuitBreaker = Cache::has($circuitBreakerKey);
        $hadFailureCount = Cache::has($failureCountKey);

        Cache::forget($circuitBreakerKey);
        Cache::forget($failureCountKey);

        if ($hadCircuitBreaker || $hadFailureCount) {
            Log::info('WebSocket断路器已重置', [
                'user_id' => $userId,
                'had_circuit_breaker' => $hadCircuitBreaker,
                'had_failure_count' => $hadFailureCount,
                'timestamp' => Carbon::now()->format('c')
            ]);
        }

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'WebSocket断路器已重置',
            'data' => [
                'user_id' => $userId,
                'had_circuit_breaker' => $hadCircuitBreaker,
                'had_failure_count' => $hadFailureCount,
                'reset_at' => Carbon::now()->format('c')
            ]
        ];
    }

    /**
     * 增加失败计数
     */
    protected function incrementFailureCount(int $userId): array
    {
        $cacheKey = "websocket_failure_count:{$userId}";
        $oldCount = Cache::get($cacheKey, 0);
        $newCount = $oldCount + 1;

        $cacheStored = Cache::put($cacheKey, $newCount, 300); // 5分钟过期

        Log::warning('WebSocket失败计数增加', [
            'user_id' => $userId,
            'old_count' => $oldCount,
            'new_count' => $newCount,
            'threshold' => self::CIRCUIT_BREAKER_THRESHOLD,
            'cache_stored' => $cacheStored,
            'timestamp' => Carbon::now()->format('c')
        ]);

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'WebSocket失败计数已增加',
            'data' => [
                'user_id' => $userId,
                'old_count' => $oldCount,
                'new_count' => $newCount,
                'threshold' => self::CIRCUIT_BREAKER_THRESHOLD,
                'should_open_breaker' => $newCount >= self::CIRCUIT_BREAKER_THRESHOLD
            ]
        ];
    }

    /**
     * 检查是否应该开启断路器
     */
    protected function shouldOpenCircuitBreaker(int $userId): array
    {
        $cacheKey = "websocket_failure_count:{$userId}";
        $count = Cache::get($cacheKey, 0);
        $shouldOpen = $count >= self::CIRCUIT_BREAKER_THRESHOLD;

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => $shouldOpen ? '应该开启断路器' : '不需要开启断路器',
            'data' => [
                'user_id' => $userId,
                'failure_count' => $count,
                'threshold' => self::CIRCUIT_BREAKER_THRESHOLD,
                'should_open' => $shouldOpen
            ]
        ];
    }

    /**
     * 获取用户的降级消息（供客户端轮询使用）
     */
    public function getFallbackMessages(int $userId, ?string $lastMessageId = null): array
    {
        $requestId = uniqid('get_fallback_');
        $startTime = microtime(true);

        try {
            $cacheKey = "websocket_fallback:{$userId}";
            $allMessages = Cache::get($cacheKey, []);
            $originalCount = count($allMessages);

            // 如果提供了lastMessageId，只返回该ID之后的消息
            $filteredMessages = $allMessages;
            $foundIndex = -1;

            if ($lastMessageId) {
                foreach ($allMessages as $index => $message) {
                    if ($message['id'] === $lastMessageId) {
                        $foundIndex = $index;
                        break;
                    }
                }

                if ($foundIndex >= 0) {
                    $filteredMessages = array_slice($allMessages, $foundIndex + 1);
                }
            }

            $duration = round((microtime(true) - $startTime) * 1000, 2);

            Log::info('获取降级消息成功', [
                'request_id' => $requestId,
                'user_id' => $userId,
                'last_message_id' => $lastMessageId,
                'original_count' => $originalCount,
                'filtered_count' => count($filteredMessages),
                'found_index' => $foundIndex,
                'duration_ms' => $duration,
                'timestamp' => Carbon::now()->format('c')
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取降级消息成功',
                'data' => [
                    'messages' => $filteredMessages,
                    'total_count' => count($filteredMessages),
                    'has_more' => $foundIndex >= 0 && count($filteredMessages) > 0,
                    'timestamp' => Carbon::now()->format('c')
                ]
            ];

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);

            Log::error('获取降级消息失败', [
                'request_id' => $requestId,
                'user_id' => $userId,
                'last_message_id' => $lastMessageId,
                'duration_ms' => $duration,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => Carbon::now()->format('c')
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取降级消息失败',
                'data' => null
            ];
        }
    }

    /**
     * 清除用户的降级消息
     */
    public function clearFallbackMessages(int $userId): bool
    {
        try {
            $cacheKey = "websocket_fallback:{$userId}";
            Cache::forget($cacheKey);

            Log::info('清除降级消息成功', [
                'user_id' => $userId
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('清除降级消息失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 获取WebSocket连接状态和统计信息
     */
    public function getConnectionStatus(int $userId): array
    {
        try {
            $circuitBreakerResult = $this->isCircuitBreakerOpen($userId);
            $circuitBreakerOpen = $circuitBreakerResult['data']['is_open'];
            $failureCount = Cache::get("websocket_failure_count:{$userId}", 0);
            $fallbackMessageCount = count(Cache::get("websocket_fallback:{$userId}", []));

            return [
                'user_id' => $userId,
                'circuit_breaker_open' => $circuitBreakerOpen,
                'failure_count' => $failureCount,
                'fallback_message_count' => $fallbackMessageCount,
                'max_retry_attempts' => self::MAX_RETRY_ATTEMPTS,
                'circuit_breaker_threshold' => self::CIRCUIT_BREAKER_THRESHOLD,
                'timestamp' => Carbon::now()->format('c')
            ];

        } catch (\Exception $e) {
            Log::error('获取连接状态失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'user_id' => $userId,
                'error' => '获取状态失败',
                'timestamp' => Carbon::now()->format('c')
            ];
        }
    }
}
