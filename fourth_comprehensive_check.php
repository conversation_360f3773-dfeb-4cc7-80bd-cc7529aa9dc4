<?php

echo "=== 第四次全面深度检查：包括所有可能的遗漏点 ===\n\n";

// 扩展检查范围，包括所有可能的目录和文件类型
$allDirectories = [
    'php/api' => ['php', 'blade.php'],
    'php/aiapi' => ['php', 'json', 'md'],
    'php/backend' => ['php'],
    'web-tool' => ['js', 'vue', 'ts', 'html', 'json'],
    'py-tool' => ['py', 'json', 'yaml', 'yml'],
    '.cursor/rules' => ['mdc', 'md'],
    'config' => ['php', 'json', 'yaml', 'yml'],
    'routes' => ['php'],
    'docs' => ['md', 'mdc', 'txt'],
    'scripts' => ['php', 'sh', 'bat']
];

// 需要检查的字段模式（更精确）
$fieldPatterns = [
    'character_avatar' => [
        'description' => 'CharacterLibrary的avatar字段引用',
        'patterns' => [
            '/character.*->avatar\b/i',
            '/avatar.*character/i',
            '/CharacterLibrary.*avatar/i',
            '/"avatar".*character/i',
            '/\'avatar\'.*character/i'
        ]
    ],
    'character_is_active' => [
        'description' => 'CharacterLibrary的is_active字段引用',
        'patterns' => [
            '/character.*->is_active\b/i',
            '/is_active.*character/i',
            '/CharacterLibrary.*is_active/i'
        ]
    ],
    'style_prompt_template' => [
        'description' => 'StyleLibrary的prompt_template字段引用',
        'patterns' => [
            '/style.*->prompt_template\b/i',
            '/prompt_template.*style/i',
            '/StyleLibrary.*prompt_template/i',
            '/"prompt_template"/i',
            '/\'prompt_template\'/i'
        ]
    ],
    'style_config' => [
        'description' => 'StyleLibrary的style_config字段引用',
        'patterns' => [
            '/style.*->style_config\b/i',
            '/style_config.*style/i',
            '/StyleLibrary.*style_config/i',
            '/"style_config"/i',
            '/\'style_config\'/i'
        ]
    ],
    'style_category' => [
        'description' => 'StyleLibrary的category字段引用',
        'patterns' => [
            '/style.*->category\b/i',
            '/category.*style/i',
            '/StyleLibrary.*category/i',
            '/"category".*style/i',
            '/\'category\'.*style/i'
        ]
    ],
    'style_is_active' => [
        'description' => 'StyleLibrary的is_active字段引用',
        'patterns' => [
            '/style.*->is_active\b/i',
            '/is_active.*style/i',
            '/StyleLibrary.*is_active/i'
        ]
    ]
];

function checkFileForAllPatterns($filePath, $patterns) {
    if (!file_exists($filePath) || !is_readable($filePath)) {
        return [];
    }
    
    $content = file_get_contents($filePath);
    $lines = explode("\n", $content);
    $matches = [];
    
    // 跳过二进制文件和过大的文件
    if (!mb_check_encoding($content, 'UTF-8') || strlen($content) > 1024 * 1024) {
        return [];
    }
    
    foreach ($patterns as $fieldName => $patternInfo) {
        foreach ($patternInfo['patterns'] as $pattern) {
            if (preg_match_all($pattern, $content, $regexMatches, PREG_OFFSET_CAPTURE)) {
                foreach ($regexMatches[0] as $match) {
                    $offset = $match[1];
                    $lineNumber = substr_count(substr($content, 0, $offset), "\n") + 1;
                    $lineContent = trim($lines[$lineNumber - 1]);
                    
                    // 过滤掉注释行
                    if (strpos($lineContent, '//') === 0 || 
                        strpos($lineContent, '*') === 0 || 
                        strpos($lineContent, '#') === 0) {
                        continue;
                    }
                    
                    $matches[] = [
                        'field' => $fieldName,
                        'line' => $lineNumber,
                        'content' => $lineContent,
                        'match' => $match[0],
                        'pattern' => $pattern
                    ];
                }
            }
        }
    }
    
    return $matches;
}

function scanDirectoryForAllPatterns($dir, $extensions, $patterns) {
    $results = [];
    
    if (!is_dir($dir)) {
        return $results;
    }
    
    try {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $fileName = $file->getFilename();
                $extension = $file->getExtension();
                
                // 检查文件扩展名
                if (in_array($extension, $extensions) || 
                    in_array($fileName, ['README.md', 'CHANGELOG.md', '.env', '.env.example'])) {
                    
                    $filePath = $file->getPathname();
                    $matches = checkFileForAllPatterns($filePath, $patterns);
                    if (!empty($matches)) {
                        $results[$filePath] = $matches;
                    }
                }
            }
        }
    } catch (Exception $e) {
        echo "   ⚠️  扫描目录 $dir 时出错: " . $e->getMessage() . "\n";
    }
    
    return $results;
}

// 执行全面检查
$allResults = [];
$totalFiles = 0;
$scannedDirs = 0;

foreach ($allDirectories as $dir => $extensions) {
    if (is_dir($dir)) {
        echo "🔍 深度扫描目录: $dir\n";
        $results = scanDirectoryForAllPatterns($dir, $extensions, $fieldPatterns);
        $scannedDirs++;
        
        if (!empty($results)) {
            foreach ($results as $filePath => $matches) {
                $relativePath = str_replace(getcwd() . DIRECTORY_SEPARATOR, '', $filePath);
                echo "  📁 发现问题文件: $relativePath\n";
                
                foreach ($matches as $match) {
                    echo "    ❌ 第{$match['line']}行 - {$match['field']}: {$match['match']}\n";
                    echo "       内容: {$match['content']}\n";
                    echo "       匹配模式: {$match['pattern']}\n";
                }
                echo "\n";
            }
            $allResults = array_merge($allResults, $results);
        } else {
            echo "  ✅ 该目录下没有发现相关字段引用\n";
        }
        echo "\n";
    } else {
        echo "⚠️  目录不存在: $dir\n\n";
    }
}

// 特别检查一些重要文件
echo "=== 特别检查重要文件 ===\n";
$importantFiles = [
    '.env',
    '.env.example',
    'composer.json',
    'package.json',
    'README.md',
    'CHANGELOG.md'
];

foreach ($importantFiles as $file) {
    if (file_exists($file)) {
        echo "🔍 检查重要文件: $file\n";
        $matches = checkFileForAllPatterns($file, $fieldPatterns);
        if (!empty($matches)) {
            echo "  发现问题:\n";
            foreach ($matches as $match) {
                echo "    ❌ 第{$match['line']}行 - {$match['field']}: {$match['match']}\n";
            }
            $allResults[$file] = $matches;
        } else {
            echo "  ✅ 没有发现问题\n";
        }
    }
}

echo "\n=== 第四次全面检查总结 ===\n";
echo "📊 扫描统计:\n";
echo "  - 扫描目录数: $scannedDirs\n";
echo "  - 检查文件类型: php, js, vue, ts, html, json, yaml, yml, py, md, mdc, txt\n";
echo "  - 检查模式数: " . count($fieldPatterns) . "\n\n";

if (empty($allResults)) {
    echo "🎉🎉🎉 完美！第四次全面深度检查没有发现任何遗漏！🎉🎉🎉\n\n";
    echo "✅ 所有目录已扫描\n";
    echo "✅ 所有文件类型已检查\n";
    echo "✅ 所有字段引用已清理\n";
    echo "✅ 包括前端、后端、配置、文档等所有文件\n\n";
    echo "🏆 字段修改任务100%完成，无任何遗漏！\n";
} else {
    echo "⚠️  发现 " . count($allResults) . " 个文件中还有字段引用需要处理：\n";
    foreach ($allResults as $filePath => $matches) {
        $relativePath = str_replace(getcwd() . DIRECTORY_SEPARATOR, '', $filePath);
        echo "  - $relativePath (" . count($matches) . " 处)\n";
    }
}

echo "\n=== 第四次检查完成 ===\n";
