<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\CharacterLibrary;
use App\Models\UserCharacterBinding;
use App\Models\UserFile;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

/**
 * 角色创建功能扩展测试用例
 * 测试CharacterController的扩展功能
 */
class CharacterControllerExtensionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $character;
    protected $project;
    protected $file;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create();
        
        // 创建测试角色
        $this->character = CharacterLibrary::factory()->create([
            'name' => '测试角色',
            'style' => '写实风3.0',
            'status' => 'published'
        ]);
        
        // 创建测试项目
        $this->project = Project::factory()->create([
            'user_id' => $this->user->id,
            'name' => '测试项目'
        ]);
        
        // 创建测试文件
        $this->file = UserFile::factory()->create([
            'user_id' => $this->user->id,
            'file_name' => 'test-character.jpg',
            'mime_type' => 'image/jpeg',
            'file_url' => 'https://example.com/test-character.jpg'
        ]);
    }



    /**
     * 测试新增的createFromFile接口 - 基础功能
     */
    public function testCreateFromFile()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => $this->file->id,
                'style' => '动漫可爱3.0',
                'project_id' => $this->project->id,
                'publish_to_library' => true,
                'auto_bind' => false
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'character_id',
                        'name',
                        'image_url',
                        'style',
                        'created_at'
                    ]
                ]);

        // 验证数据库中创建了角色记录
        $this->assertDatabaseHas('character_library', [
            'style' => '动漫可爱3.0',
            'project_id' => $this->project->id,
            'source_file_id' => $this->file->id
        ]);
    }

    /**
     * 测试createFromFile接口 - 文件验证
     */
    public function testCreateFromFileValidation()
    {
        // 测试文件不存在
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => 'non_existent_file'
            ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['file_id']);

        // 创建非图片文件
        $textFile = UserFile::factory()->create([
            'user_id' => $this->user->id,
            'mime_type' => 'text/plain'
        ]);

        // 测试非图片文件
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => $textFile->id
            ]);

        $response->assertStatus(400)
                ->assertJson([
                    'message' => '文件必须是图片格式'
                ]);

        // 创建其他用户的文件
        $otherUserFile = UserFile::factory()->create([
            'user_id' => User::factory()->create()->id,
            'mime_type' => 'image/jpeg'
        ]);

        // 测试无权访问的文件
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => $otherUserFile->id
            ]);

        $response->assertStatus(403)
                ->assertJson([
                    'message' => '无权访问该文件'
                ]);
    }

    /**
     * 测试扩展后的bind接口 - 基础功能
     */
    public function testBindWithExtendedParams()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/bind', [
                'character_id' => $this->character->id,
                'reason' => '分镜绑定测试',
                'storyboard_position_id' => 'pos_123',
                'binding_context' => 'storyboard',
                'auto_bind' => true,
                'compatibility_check' => true
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'binding_id',
                        'character_id',
                        'character_name',
                        'storyboard_position_id',
                        'binding_context',
                        'created_at'
                    ]
                ]);

        // 验证数据库中创建了绑定记录
        $this->assertDatabaseHas('user_character_bindings', [
            'user_id' => $this->user->id,
            'character_id' => $this->character->id,
            'storyboard_position_id' => 'pos_123',
            'binding_context' => 'storyboard'
        ]);
    }

    /**
     * 测试bind接口 - 重复绑定检查
     */
    public function testBindDuplicateCheck()
    {
        // 先创建一个绑定
        UserCharacterBinding::create([
            'user_id' => $this->user->id,
            'character_id' => $this->character->id,
            'binding_name' => $this->character->name,
            'is_active' => true
        ]);

        // 尝试重复绑定
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/bind', [
                'character_id' => $this->character->id,
                'reason' => '重复绑定测试'
            ]);

        $response->assertStatus(400)
                ->assertJson([
                    'message' => '角色已经绑定'
                ]);
    }

    /**
     * 测试自动绑定功能
     */
    public function testAutoBind()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => $this->file->id,
                'style' => '写实风3.0',
                'project_id' => $this->project->id,
                'auto_bind' => true,
                'storyboard_position_id' => 'pos_456'
            ]);

        $response->assertStatus(200);

        $characterId = $response->json('data.character_id');

        // 验证自动绑定是否成功
        $this->assertDatabaseHas('user_character_bindings', [
            'user_id' => $this->user->id,
            'character_id' => $characterId,
            'storyboard_position_id' => 'pos_456',
            'binding_context' => 'project',
            'is_active' => true
        ]);
    }

    /**
     * 测试兼容性检查功能
     */
    public function testCompatibilityCheck()
    {
        // 模拟兼容性检查通过的情况
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/bind', [
                'character_id' => $this->character->id,
                'storyboard_position_id' => 'pos_compatible',
                'binding_context' => 'storyboard',
                'compatibility_check' => true
            ]);

        $response->assertStatus(200);

        // 验证绑定成功
        $this->assertDatabaseHas('user_character_bindings', [
            'user_id' => $this->user->id,
            'character_id' => $this->character->id,
            'storyboard_position_id' => 'pos_compatible'
        ]);
    }



    /**
     * 测试发布到角色库功能
     */
    public function testPublishToLibrary()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => $this->file->id,
                'style' => '写实风3.0',
                'publish_to_library' => true
            ]);

        $response->assertStatus(200);

        $characterId = $response->json('data.character_id');

        // 验证角色被标记为公开
        $this->assertDatabaseHas('character_library', [
            'id' => $characterId,
            'is_public' => true
        ]);
    }

    /**
     * 测试认证失败情况
     */
    public function testUnauthenticatedAccess()
    {
        // 测试未认证用户访问bind接口
        $response = $this->postJson('/py-api/characters/bind', [
            'character_id' => 1
        ]);

        $response->assertStatus(401);

        // 测试未认证用户访问createFromFile接口
        $response = $this->postJson('/py-api/characters/create-from-file', [
            'file_id' => $this->file->id
        ]);

        $response->assertStatus(401);

        // 测试未认证用户访问bind接口
        $response = $this->postJson('/py-api/characters/bind', [
            'character_id' => $this->character->id
        ]);

        $response->assertStatus(401);
    }

    /**
     * 测试角色不存在的情况
     */
    public function testCharacterNotFound()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/py-api/characters/bind', [
                'character_id' => 99999 // 不存在的角色ID
            ]);

        $response->assertStatus(404)
                ->assertJson([
                    'message' => '角色不存在'
                ]);
    }

    /**
     * 测试完整的角色创建到绑定流程
     */
    public function testCompleteCharacterCreationFlow()
    {
        // 第一步：基于文件创建角色
        $createResponse = $this->actingAs($this->user)
            ->postJson('/py-api/characters/create-from-file', [
                'file_id' => $this->file->id,
                'style' => '武侠古风3.0',
                'project_id' => $this->project->id,
                'publish_to_library' => false,
                'auto_bind' => false
            ]);

        $createResponse->assertStatus(200);
        $characterId = $createResponse->json('data.character_id');

        // 第二步：手动绑定角色到分镜位置
        $bindResponse = $this->actingAs($this->user)
            ->postJson('/py-api/characters/bind', [
                'character_id' => $characterId,
                'reason' => '完整流程测试绑定',
                'storyboard_position_id' => 'pos_flow_test',
                'binding_context' => 'storyboard',
                'compatibility_check' => true
            ]);

        $bindResponse->assertStatus(200);

        // 验证整个流程的数据完整性
        $this->assertDatabaseHas('character_library', [
            'id' => $characterId,
            'style' => '武侠古风3.0',
            'project_id' => $this->project->id,
            'source_file_id' => $this->file->id,
            'is_public' => false
        ]);

        $this->assertDatabaseHas('user_character_bindings', [
            'user_id' => $this->user->id,
            'character_id' => $characterId,
            'storyboard_position_id' => 'pos_flow_test',
            'binding_context' => 'storyboard',
            'is_active' => true
        ]);
    }
}
