# 环境切换机制架构文档

## 🚨 架构边界规范

基于 `index-new.mdc` 架构规范，本项目是一个完整的AI视频创作工具生态系统，包含五大核心组件：

1. **Py视频创作工具**
2. **WEB网页工具**  
3. **管理后台**
4. **工具API接口服务** ← **环境切换机制在此实现**
5. **AI服务集成模拟返回数据服务** ← 仅负责模拟，不包含环境切换
6. **第三方服务集成模拟返回数据服务** ← 仅负责模拟，不包含环境切换

## 📋 环境切换机制实现位置

### ✅ 工具API接口服务 (php/api)

**职责：环境切换机制的唯一实现位置**

- **配置文件**：`php/api/config/ai.php`
- **服务客户端**：
  - `php/api/app/Services/AiServiceClient.php`
  - `php/api/app/Services/ThirdPartyServiceClient.php`
- **环境变量**：`php/api/.env.example`

### ❌ 模拟服务 (php/aiapi 和 php/thirdapi)

**职责：仅负责模拟，不包含环境切换逻辑**

- **aiapi配置**：`php/aiapi/config/config.php` - 只包含 `mock_config`
- **thirdapi配置**：`php/thirdapi/config/config.php` - 只包含 `mock_config`
- **不包含**：任何环境切换相关的配置或逻辑

## 🔄 环境切换流程

### 开发环境 (mock模式)
```
用户请求 → 工具API接口服务 → AiServiceClient/ThirdPartyServiceClient 
         → 检查 AI_SERVICE_MODE=mock → 调用模拟服务 → 返回模拟响应
```

### 生产环境 (real模式)
```
用户请求 → 工具API接口服务 → AiServiceClient/ThirdPartyServiceClient 
         → 检查 AI_SERVICE_MODE=real → 调用真实AI平台 → 返回真实响应
```

## ⚙️ 配置结构

### 工具API配置 (php/api/config/ai.php)

```php
return [
    // 🚨 环境切换配置
    'service_mode' => env('AI_SERVICE_MODE', 'mock'), // mock | real
    
    // 模拟服务配置（开发环境）
    'mock_service' => [
        'base_url' => env('AI_MOCK_URL', 'https://aiapi.tiptop.cn'),
        'timeout' => env('AI_MOCK_TIMEOUT', 30),
    ],
    
    // 真实服务配置（生产环境）
    'real_service' => [
        'timeout' => env('AI_REAL_TIMEOUT', 60),
    ],
    
    // AI平台配置（支持环境切换）
    'platforms' => [
        'deepseek' => [
            'mock_endpoint' => '/deepseek/chat/completions',
            'real_api' => [
                'base_url' => env('DEEPSEEK_API_URL', 'https://api.deepseek.com'),
                'api_key' => env('DEEPSEEK_API_KEY', ''),
                'endpoint' => '/chat/completions',
            ],
        ],
        // ... 其他平台
    ],
    
    // 第三方服务配置
    'third_party_config' => [
        'service_mode' => env('THIRD_PARTY_MODE', 'mock'),
        'mock_service' => [
            'base_url' => env('THIRD_PARTY_MOCK_URL', 'https://thirdapi.tiptop.cn'),
        ],
        'platforms' => [
            'wechat' => [
                'mock_endpoint' => '/wechat/oauth/authorize',
                'real_api' => [
                    'app_id' => env('WECHAT_APP_ID', ''),
                    'app_secret' => env('WECHAT_APP_SECRET', ''),
                ],
            ],
            // ... 其他平台
        ],
    ],
];
```

### 环境变量配置 (php/api/.env.example)

```bash
# 🚨 AI服务环境切换配置
AI_SERVICE_MODE=mock  # mock | real
AI_MOCK_URL=https://aiapi.tiptop.cn
AI_MOCK_TIMEOUT=30
AI_REAL_TIMEOUT=60

# 🚨 第三方服务环境切换配置
THIRD_PARTY_MODE=mock  # mock | real
THIRD_PARTY_MOCK_URL=https://thirdapi.tiptop.cn
THIRD_PARTY_MOCK_TIMEOUT=30

# 真实API配置（仅在 real 模式时使用）
DEEPSEEK_API_KEY=
LIBLIB_API_KEY=
KLING_API_KEY=
MINIMAX_API_KEY=
VOLCENGINE_API_KEY=
WECHAT_APP_ID=
WECHAT_APP_SECRET=
ALIPAY_APP_ID=
ALIPAY_PRIVATE_KEY=
```

### 模拟服务配置（仅包含模拟配置）

**aiapi配置示例**：
```php
$aiPlatforms = [
    'deepseek' => [
        'name' => 'DeepSeek',
        'description' => '剧情生成和分镜脚本专家',
        'mock_config' => [
            'enabled' => true,
            'response_delay' => [1000, 3000],
            'success_rate' => 95,
            'simulate_real_behavior' => true,
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ],
        // 不包含 real_api 配置
    ],
];
```

## 💻 使用示例

### AI服务调用

```php
use App\Services\AiServiceClient;

// 自动根据环境切换调用模拟或真实服务
$response = AiServiceClient::call('deepseek', [
    'model' => 'deepseek-chat',
    'messages' => [
        ['role' => 'user', 'content' => '请生成一个科幻故事剧情']
    ]
]);

// 检查当前模式
$mode = AiServiceClient::getServiceMode(); // 'mock' 或 'real'
$isMock = AiServiceClient::isMockMode(); // true 或 false
```

### 第三方服务调用

```php
use App\Services\PyApi\ThirdPartyServiceClient;

// 自动根据环境切换调用模拟或真实服务
$response = ThirdPartyServiceClient::call('wechat', [
    'action' => 'oauth_authorize',
    'appid' => 'mock_app_id',
    'redirect_uri' => 'https://yourdomain.com/callback'
]);

// 检查当前模式
$mode = ThirdPartyServiceClient::getServiceMode(); // 'mock' 或 'real'
```

## 🔍 架构验证

运行验证脚本确认架构正确性：

```bash
php scripts/validate-environment-switching.php
```

验证内容：
- ✅ 环境切换配置在工具API接口服务中实现
- ✅ 模拟服务不包含环境切换逻辑
- ✅ 服务客户端正确实现环境切换
- ✅ 环境变量配置完整

## 🎯 关键优势

1. **架构清晰**：职责分离明确，环境切换逻辑集中在工具API中
2. **配置统一**：所有环境切换配置在一个地方管理
3. **开发效率**：本地开发无需配置真实API密钥
4. **成本控制**：开发阶段不产生任何真实费用
5. **安全隔离**：模拟环境与真实环境完全隔离
6. **易于维护**：环境切换逻辑集中，便于维护和升级

## ⚠️ 重要提醒

- **开发环境**：使用 `mock` 模式，不产生真实费用
- **生产环境**：使用 `real` 模式，产生真实费用，需谨慎操作
- **配置检查**：切换环境前务必检查相关配置的正确性
- **架构边界**：严格遵守架构边界，不在模拟服务中添加环境切换逻辑
