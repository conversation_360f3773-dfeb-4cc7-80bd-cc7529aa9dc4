<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 用户角色绑定模型
 * 
 * @property int $id
 * @property int $user_id
 * @property int $character_id
 * @property string $binding_name
 * @property string $binding_reason

 * @property array $custom_config
 * @property bool $is_active
 * @property bool $is_favorite
 * @property int $usage_count
 * @property \Carbon\Carbon $last_used_at
 * @property float $user_rating
 * @property string $user_feedback
 * @property array $usage_stats
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class UserCharacterBinding extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'user_character_bindings';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'character_id',
        'binding_name',
        'binding_reason',

        'custom_config',
        'is_active',
        'is_favorite',
        'usage_count',
        'last_used_at',
        'user_rating',
        'user_feedback',
        'usage_stats'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'custom_config' => 'array',
        'is_active' => 'boolean',
        'is_favorite' => 'boolean',
        'usage_count' => 'integer',
        'last_used_at' => 'datetime',
        'user_rating' => 'decimal:2',
        'usage_stats' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'is_active' => true,
        'is_favorite' => false,
        'usage_count' => 0
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联角色
     */
    public function character(): BelongsTo
    {
        return $this->belongsTo(CharacterLibrary::class, 'character_id');
    }

    /**
     * 使用角色
     */
    public function use(): void
    {
        $this->increment('usage_count');
        $this->last_used_at = Carbon::now();
        $this->save();

        // 更新使用统计
        $this->updateUsageStats();
    }

    /**
     * 设置为收藏
     */
    public function setFavorite(bool $favorite = true): void
    {
        $this->is_favorite = $favorite;
        $this->save();
    }

    /**
     * 评分
     */
    public function rate(float $rating, string $feedback = null): void
    {
        $this->user_rating = $rating;
        $this->user_feedback = $feedback;
        $this->save();

        // 更新角色的整体评分
        $this->character->updateRating($rating);
    }

    /**
     * 获取自定义配置
     */
    public function getCustomConfig(string $key, $default = null)
    {
        return data_get($this->custom_config, $key, $default);
    }

    /**
     * 设置自定义配置
     */
    public function setCustomConfig(string $key, $value): void
    {
        $config = $this->custom_config ?? [];
        data_set($config, $key, $value);
        $this->custom_config = $config;
    }

    /**
     * 获取使用统计
     */
    public function getUsageStats(string $key, $default = null)
    {
        return data_get($this->usage_stats, $key, $default);
    }

    /**
     * 更新使用统计
     */
    public function updateUsageStats(): void
    {
        $stats = $this->usage_stats ?? [];
        
        // 更新今日使用次数
        $today = Carbon::now()->format('Y-m-d');
        $stats['daily'][$today] = ($stats['daily'][$today] ?? 0) + 1;
        
        // 更新本周使用次数
        $week = Carbon::now()->format('Y-W');
        $stats['weekly'][$week] = ($stats['weekly'][$week] ?? 0) + 1;
        
        // 更新本月使用次数
        $month = Carbon::now()->format('Y-m');
        $stats['monthly'][$month] = ($stats['monthly'][$month] ?? 0) + 1;
        
        // 保留最近30天的数据
        if (isset($stats['daily'])) {
            $cutoff = Carbon::now()->subDays(30)->format('Y-m-d');
            $stats['daily'] = array_filter($stats['daily'], function($key) use ($cutoff) {
                return $key >= $cutoff;
            }, ARRAY_FILTER_USE_KEY);
        }
        
        $this->usage_stats = $stats;
        $this->save();
    }

    /**
     * 获取绑定显示名称
     */
    public function getDisplayName(): string
    {
        return $this->binding_name ?: $this->character->name;
    }



    /**
     * 检查是否最近使用过
     */
    public function isRecentlyUsed(int $days = 7): bool
    {
        return $this->last_used_at && $this->last_used_at->diffInDays(Carbon::now()) <= $days;
    }

    /**
     * 获取使用频率
     */
    public function getUsageFrequency(): string
    {
        if ($this->usage_count === 0) {
            return 'never';
        }
        
        $daysSinceCreated = $this->created_at->diffInDays(Carbon::now());
        if ($daysSinceCreated === 0) {
            $daysSinceCreated = 1;
        }
        
        $avgUsagePerDay = $this->usage_count / $daysSinceCreated;
        
        if ($avgUsagePerDay >= 1) {
            return 'daily';
        } elseif ($avgUsagePerDay >= 0.5) {
            return 'frequent';
        } elseif ($avgUsagePerDay >= 0.1) {
            return 'regular';
        } else {
            return 'occasional';
        }
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按角色筛选
     */
    public function scopeByCharacter($query, $characterId)
    {
        return $query->where('character_id', $characterId);
    }

    /**
     * 作用域：活跃的绑定
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：收藏的绑定
     */
    public function scopeFavorite($query)
    {
        return $query->where('is_favorite', true);
    }

    /**
     * 作用域：最近使用的绑定
     */
    public function scopeRecentlyUsed($query, $days = 7)
    {
        return $query->where('last_used_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * 作用域：按使用次数排序
     */
    public function scopeByUsage($query)
    {
        return $query->orderBy('usage_count', 'desc')->orderBy('last_used_at', 'desc');
    }

    /**
     * 作用域：按评分排序
     */
    public function scopeByRating($query)
    {
        return $query->orderBy('user_rating', 'desc');
    }

    /**
     * 作用域：有评分的绑定
     */
    public function scopeRated($query)
    {
        return $query->whereNotNull('user_rating');
    }
}
