<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Services\Common\TransactionManager;
use Illuminate\Support\Facades\Log;

/**
 * WebSocket会话模型
 *
 * @property int $id
 * @property string $session_id
 * @property int $user_id
 * @property string $client_type
 * @property string $business_type
 * @property string $client_version
 * @property string $connection_ip
 * @property string $user_agent
 * @property string $status
 * @property array $connection_info
 * @property array $subscribed_events
 * @property \Carbon\Carbon $connected_at
 * @property \Carbon\Carbon $last_ping_at
 * @property \Carbon\Carbon $disconnected_at
 * @property int $message_count
 * @property string $disconnect_reason
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class WebSocketSession extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'websocket_sessions';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'session_id',
        'user_id',
        'client_type',
        'business_type',
        'client_version',
        'connection_ip',
        'user_agent',
        'status',
        'connection_info',
        'subscribed_events',
        'connected_at',
        'last_ping_at',
        'disconnected_at',
        'message_count',
        'disconnect_reason'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'connection_info' => 'array',
        'subscribed_events' => 'array',
        'connected_at' => 'datetime',
        'last_ping_at' => 'datetime',
        'disconnected_at' => 'datetime',
        'message_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'client_type' => 'unknown',
        'status' => 'pending',  // 🔧 修复：默认状态改为pending
        'message_count' => 0
    ];

    /**
     * 连接状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_CONNECTED = 'connected';
    const STATUS_DISCONNECTED = 'disconnected';
    const STATUS_TIMEOUT = 'timeout';

    /**
     * 客户端类型常量
     */
    const CLIENT_TYPE_PYTHON_TOOL = 'python_tool';
    const CLIENT_TYPE_WEB_BROWSER = 'web_browser';
    const CLIENT_TYPE_MOBILE_APP = 'mobile_app';
    const CLIENT_TYPE_UNKNOWN = 'unknown';

    /**
     * 业务类型常量
     */
    const BUSINESS_TYPE_TEXT_GENERATION = 'text_generation';
    const BUSINESS_TYPE_STORYBOARD_GENERATION = 'storyboard_generation';
    const BUSINESS_TYPE_PROJECT_CREATION = 'project_creation';
    const BUSINESS_TYPE_CHARACTER_CREATION = 'character_creation';
    const BUSINESS_TYPE_VOICE_AUDITION = 'voice_audition';

    /**
     * 事件类型常量
     */
    const EVENT_AI_GENERATION_PROGRESS = 'ai_generation_progress';
    const EVENT_POINTS_CHANGED = 'points_changed';

    /**
     * 获取AI生成完成事件类型
     * 根据任务类型动态获取对应的完成事件类型
     *
     * @param string $taskType 任务类型
     * @return string
     */
    public static function getAiGenerationCompletedEvent(string $taskType): string
    {
        $eventType = \App\Helpers\EventTypeHelper::getCompletedEventType($taskType);
        return $eventType ?? 'ai_generation_completed'; // 默认值作为后备
    }

    /**
     * 获取AI生成失败事件类型
     * 根据任务类型动态获取对应的失败事件类型
     *
     * @param string $taskType 任务类型
     * @return string
     */
    public static function getAiGenerationFailedEvent(string $taskType): string
    {
        $eventType = \App\Helpers\EventTypeHelper::getFailedEventType($taskType);
        return $eventType ?? 'ai_generation_failed'; // 默认值作为后备
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 更新心跳时间 (立即写数据库 - 已废弃，建议使用批量更新)
     * @deprecated 建议使用 batchUpdateHeartbeat() 进行批量更新以提升性能
     */
    public function updatePing(): void
    {
        $this->last_ping_at = Carbon::now();
        $this->save();
    }

    /**
     * 批量更新用户心跳时间
     * 根据用户ID和最新心跳时间戳批量更新数据库
     *
     * @param array $userHeartbeats 格式: [user_id => latest_ping_timestamp, ...]
     * @return int 更新的记录数
     */
    public static function batchUpdateHeartbeat(array $userHeartbeats): int
    {
        if (empty($userHeartbeats)) {
            return 0;
        }

        $updatedCount = 0;

        try {
            TransactionManager::begin('WebSocketSession.batchUpdateHeartbeat');

            foreach ($userHeartbeats as $userId => $pingTimestamp) {
                $pingTime = Carbon::createFromTimestamp($pingTimestamp);

                $updated = self::where('user_id', $userId)
                    ->where('status', self::STATUS_CONNECTED)
                    ->update(['last_ping_at' => $pingTime]);

                $updatedCount += $updated;
            }

            TransactionManager::commit('WebSocketSession.batchUpdateHeartbeat');

            Log::info('WebSocket批量心跳更新成功', [
                'total_users' => count($userHeartbeats),
                'updated_sessions' => $updatedCount,
                'timestamp' => Carbon::now()->format('c')
            ]);

            return $updatedCount;

        } catch (\Exception $e) {
            TransactionManager::rollback('WebSocketSession.batchUpdateHeartbeat', $e->getMessage());

            Log::error('WebSocket批量心跳更新失败', [
                'total_users' => count($userHeartbeats),
                'error' => $e->getMessage(),
                'timestamp' => Carbon::now()->format('c')
            ]);

            throw $e;
        }
    }

    /**
     * 🚀 按session_id批量更新心跳时间 (修复多连接支持)
     * 根据session_id和最新心跳时间戳批量更新数据库
     *
     * @param array $sessionHeartbeats 格式: [session_id => latest_ping_timestamp, ...]
     * @return int 更新的记录数
     */
    public static function batchUpdateHeartbeatBySessionId(array $sessionHeartbeats): int
    {
        if (empty($sessionHeartbeats)) {
            return 0;
        }

        $updatedCount = 0;

        try {
            TransactionManager::begin('WebSocketSession.batchUpdateHeartbeatBySessionId');

            foreach ($sessionHeartbeats as $sessionId => $pingTimestamp) {
                $pingTime = Carbon::createFromTimestamp($pingTimestamp);

                // 🔧 修复：按session_id精确更新，支持同用户多连接
                $updated = self::where('session_id', $sessionId)
                    ->where('status', self::STATUS_CONNECTED)
                    ->update(['last_ping_at' => $pingTime]);

                $updatedCount += $updated;
            }

            TransactionManager::commit('WebSocketSession.batchUpdateHeartbeatBySessionId');

            Log::info('WebSocket按session_id批量心跳更新成功', [
                'total_sessions' => count($sessionHeartbeats),
                'updated_sessions' => $updatedCount,
                'timestamp' => Carbon::now()->format('c')
            ]);

            return $updatedCount;

        } catch (\Exception $e) {
            TransactionManager::rollback('WebSocketSession.batchUpdateHeartbeatBySessionId', $e->getMessage());

            Log::error('WebSocket按session_id批量心跳更新失败', [
                'total_sessions' => count($sessionHeartbeats),
                'error' => $e->getMessage(),
                'timestamp' => Carbon::now()->format('c')
            ]);

            return 0;
        }
    }

    /**
     * 断开连接
     */
    public function disconnect(?string $reason = null): void
    {
        $this->status = self::STATUS_DISCONNECTED;
        $this->disconnected_at = Carbon::now();
        $this->disconnect_reason = $reason;
        $this->save();
    }

    /**
     * 标记为超时
     */
    public function markAsTimeout(): void
    {
        try {
            TransactionManager::begin('WebSocketSession.markAsTimeout');

            $this->status = self::STATUS_TIMEOUT;
            $this->disconnected_at = Carbon::now();
            $this->disconnect_reason = '连接超时';
            $this->save();

            TransactionManager::commit('WebSocketSession.markAsTimeout');

            Log::info('WebSocket会话标记为超时', [
                'session_id' => $this->session_id,
                'user_id' => $this->user_id,
                'client_type' => $this->client_type,
                'business_type' => $this->business_type
            ]);

        } catch (\Exception $e) {
            TransactionManager::rollback('WebSocketSession.markAsTimeout', $e->getMessage());

            Log::error('WebSocket会话超时标记失败', [
                'session_id' => $this->session_id,
                'user_id' => $this->user_id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 增加消息计数
     */
    public function incrementMessageCount(): void
    {
        try {
            TransactionManager::begin('WebSocketSession.incrementMessageCount');

            $this->increment('message_count');

            TransactionManager::commit('WebSocketSession.incrementMessageCount');

        } catch (\Exception $e) {
            TransactionManager::rollback('WebSocketSession.incrementMessageCount', $e->getMessage());

            Log::error('WebSocket会话消息计数增加失败', [
                'session_id' => $this->session_id,
                'user_id' => $this->user_id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 订阅事件
     */
    public function subscribeEvent(string $eventType): void
    {
        try {
            TransactionManager::begin('WebSocketSession.subscribeEvent');

            $events = $this->subscribed_events ?? [];
            if (!in_array($eventType, $events)) {
                $events[] = $eventType;
                $this->subscribed_events = $events;
                $this->save();

                Log::info('WebSocket会话订阅事件', [
                    'session_id' => $this->session_id,
                    'user_id' => $this->user_id,
                    'event_type' => $eventType,
                    'total_subscriptions' => count($events)
                ]);
            }

            TransactionManager::commit('WebSocketSession.subscribeEvent');

        } catch (\Exception $e) {
            TransactionManager::rollback('WebSocketSession.subscribeEvent', $e->getMessage());

            Log::error('WebSocket会话事件订阅失败', [
                'session_id' => $this->session_id,
                'user_id' => $this->user_id,
                'event_type' => $eventType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 取消订阅事件
     */
    public function unsubscribeEvent(string $eventType): void
    {
        try {
            TransactionManager::begin('WebSocketSession.unsubscribeEvent');

            $events = $this->subscribed_events ?? [];
            $originalCount = count($events);
            $this->subscribed_events = array_values(array_filter($events, fn($e) => $e !== $eventType));
            $this->save();

            TransactionManager::commit('WebSocketSession.unsubscribeEvent');

            if ($originalCount > count($this->subscribed_events)) {
                Log::info('WebSocket会话取消订阅事件', [
                    'session_id' => $this->session_id,
                    'user_id' => $this->user_id,
                    'event_type' => $eventType,
                    'remaining_subscriptions' => count($this->subscribed_events)
                ]);
            }

        } catch (\Exception $e) {
            TransactionManager::rollback('WebSocketSession.unsubscribeEvent', $e->getMessage());

            Log::error('WebSocket会话取消订阅事件失败', [
                'session_id' => $this->session_id,
                'user_id' => $this->user_id,
                'event_type' => $eventType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 检查是否订阅了事件
     */
    public function isSubscribedTo(string $eventType): bool
    {
        return in_array($eventType, $this->subscribed_events ?? []);
    }

    /**
     * 检查是否为Py视频创作工具客户端
     */
    public function isPythonTool(): bool
    {
        return $this->client_type === self::CLIENT_TYPE_PYTHON_TOOL;
    }

    /**
     * 检查连接是否活跃
     */
    public function isActive(): bool
    {
        if ($this->status !== self::STATUS_CONNECTED) {
            return false;
        }

        // 如果超过5分钟没有心跳，认为连接不活跃
        if ($this->last_ping_at && $this->last_ping_at->diffInMinutes(Carbon::now()) > 5) {
            return false;
        }

        return true;
    }

    /**
     * 获取连接时长（秒）
     */
    public function getConnectionDuration(): int
    {
        $endTime = $this->disconnected_at ?? Carbon::now();
        return $this->connected_at->diffInSeconds($endTime);
    }

    /**
     * 获取连接信息
     */
    public function getConnectionInfo(string $key, $default = null)
    {
        return data_get($this->connection_info, $key, $default);
    }

    /**
     * 设置连接信息
     */
    public function setConnectionInfo(string $key, $value): void
    {
        $info = $this->connection_info ?? [];
        data_set($info, $key, $value);
        $this->connection_info = $info;
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按客户端类型筛选
     */
    public function scopeByClientType($query, $clientType)
    {
        return $query->where('client_type', $clientType);
    }

    /**
     * 作用域：按业务类型筛选
     */
    public function scopeByBusinessType($query, $businessType)
    {
        return $query->where('business_type', $businessType);
    }

    /**
     * 作用域：活跃连接
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_CONNECTED)
            ->where(function($q) {
                $q->whereNull('last_ping_at')
                  ->orWhere('last_ping_at', '>', Carbon::now()->subMinutes(5));
            });
    }

    /**
     * 作用域：Py视频创作工具连接
     */
    public function scopePythonTool($query)
    {
        return $query->where('client_type', self::CLIENT_TYPE_PYTHON_TOOL);
    }

    /**
     * 作用域：超时连接
     */
    public function scopeTimeout($query)
    {
        return $query->where('status', self::STATUS_CONNECTED)
            ->where('last_ping_at', '<', Carbon::now()->subMinutes(5));
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
