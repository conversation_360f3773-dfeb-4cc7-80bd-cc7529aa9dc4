<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

/**
 * 初始化存储目录结构命令
 */
class InitStorageFolders extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'storage:init-folders {--force : 强制创建，覆盖已存在的目录}';

    /**
     * 命令描述
     */
    protected $description = '初始化文件存储目录结构';

    /**
     * 预定义的目录结构
     */
    protected $directories = [
        // 项目相关目录
        'projects',
        'projects/thumbnails',
        
        // 角色相关目录
        'characters',
        'characters/avatars',
        'characters/images',
        'characters/voices',
        'characters/configs',
        
        // 风格相关目录
        'styles',
        'styles/thumbnails',
        'styles/samples',
        'styles/templates',
        
        // 音色相关目录
        'voices',
        'voices/samples',
        'voices/models',
        'voices/configs',
        
        // 音效相关目录
        'sounds',
        'sounds/generated',
        'sounds/library',
        'sounds/temp',
        
        // 资源相关目录
        'resources',
        'resources/generated',
        'resources/generated/images',
        'resources/generated/videos',
        'resources/generated/audio',
        'resources/generated/music',
        'resources/generated/sounds',
        'resources/generated/stories',
        'resources/exports',
        'resources/cache',
        
        // 用户相关目录
        'users',
        'users/public',
        
        // 临时目录
        'temp',
        'temp/uploads',
        'temp/processing',
        'temp/cache',
    ];

    /**
     * 执行命令
     */
    public function handle()
    {
        $this->info('开始初始化存储目录结构...');

        $force = $this->option('force');

        // 如果使用 --force 参数，进行三次确认
        if ($force) {
            if (!$this->confirmForceOperation()) {
                $this->info('操作已取消。');
                return 0;
            }
        }
        $storagePath = storage_path('app/public');
        
        // 确保主存储目录存在
        if (!File::exists($storagePath)) {
            File::makeDirectory($storagePath, 0755, true);
            $this->info("创建主存储目录: {$storagePath}");
        }
        
        $created = 0;
        $skipped = 0;
        $errors = 0;
        
        foreach ($this->directories as $directory) {
            $fullPath = $storagePath . '/' . $directory;
            
            try {
                if (File::exists($fullPath)) {
                    if ($force) {
                        $this->warn("目录已存在，强制重新创建: {$directory}");
                        File::deleteDirectory($fullPath);
                        File::makeDirectory($fullPath, 0755, true);
                        $created++;
                    } else {
                        $this->line("目录已存在，跳过: {$directory}");
                        $skipped++;
                    }
                } else {
                    File::makeDirectory($fullPath, 0755, true);
                    $this->info("创建目录: {$directory}");
                    $created++;
                }
                
                // 创建 .gitkeep 文件以确保空目录被版本控制
                $gitkeepPath = $fullPath . '/.gitkeep';
                if (!File::exists($gitkeepPath)) {
                    File::put($gitkeepPath, '');
                }
                
            } catch (\Exception $e) {
                $this->error("创建目录失败: {$directory} - " . $e->getMessage());
                $errors++;
            }
        }
        
        // 创建符号链接（如果不存在）
        $this->createStorageLink();
        
        // 显示统计信息
        $this->newLine();
        $this->info('目录初始化完成！');
        $this->table(
            ['状态', '数量'],
            [
                ['创建', $created],
                ['跳过', $skipped],
                ['错误', $errors],
                ['总计', count($this->directories)]
            ]
        );
        
        if ($errors > 0) {
            $this->error('部分目录创建失败，请检查权限设置。');
            return 1;
        }
        
        $this->info('所有存储目录已成功初始化！');
        return 0;
    }
    
    /**
     * 创建存储符号链接
     */
    protected function createStorageLink()
    {
        $publicPath = base_path('public/storage');
        $storagePath = storage_path('app/public');
        
        if (File::exists($publicPath)) {
            if (is_link($publicPath)) {
                $this->info('存储符号链接已存在');
                return;
            } else {
                $this->warn('public/storage 目录已存在但不是符号链接');
                return;
            }
        }
        
        try {
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows 系统使用 mklink
                $command = 'mklink /D "' . $publicPath . '" "' . $storagePath . '"';
                exec($command, $output, $returnCode);

                if ($returnCode === 0) {
                    $this->info('存储符号链接创建成功 (Windows)');
                } else {
                    $this->warn('无法创建符号链接，请手动运行: php artisan storage:link');
                }
            } else {
                // Unix/Linux 系统使用 symlink
                symlink($storagePath, $publicPath);
                $this->info('存储符号链接创建成功 (Unix/Linux)');
            }
        } catch (\Exception $e) {
            $this->warn('无法创建符号链接: ' . $e->getMessage());
            $this->warn('请手动运行: php artisan storage:link');
        }
    }

    /**
     * 三次确认强制操作
     */
    protected function confirmForceOperation(): bool
    {
        $this->newLine();
        $this->error('⚠️  警告：您正在使用 --force 参数！');
        $this->error('⚠️  这将会删除所有现有的存储目录及其内容！');
        $this->error('⚠️  包括：项目文件、角色资源、用户上传文件等所有数据！');
        $this->newLine();

        // 第一次确认
        $this->warn('🔥 第一次确认：');
        $this->warn('这是一个破坏性操作，将永久删除以下目录中的所有文件：');
        $this->warn('- storage/app/public/projects/ (所有项目文件)');
        $this->warn('- storage/app/public/characters/ (所有角色资源)');
        $this->warn('- storage/app/public/users/ (所有用户文件)');
        $this->warn('- storage/app/public/resources/ (所有生成的资源)');
        $this->warn('- 以及其他所有存储目录...');
        $this->newLine();

        if (!$this->confirm('您确定要继续吗？这将删除所有现有文件！', false)) {
            return false;
        }

        $this->newLine();

        // 第二次确认
        $this->error('🔥 第二次确认：');
        $this->error('请再次确认：您真的要删除所有存储目录中的文件吗？');
        $this->error('删除后的文件将无法恢复！');
        $this->newLine();

        if (!$this->confirm('请输入 "yes" 确认删除所有文件', false)) {
            return false;
        }

        $this->newLine();

        // 第三次确认（最严格）
        $this->error('🔥 第三次确认（最后机会）：');
        $this->error('这是最后一次确认机会！');
        $this->error('执行后将立即开始删除所有存储目录！');
        $this->error('您确定已经备份了重要数据吗？');
        $this->newLine();

        $finalConfirm = $this->ask('请输入 "DELETE ALL FILES" 来最终确认删除操作（区分大小写）');

        if ($finalConfirm !== 'DELETE ALL FILES') {
            $this->error('确认文本不匹配，操作已取消。');
            $this->info('提示：需要输入完全匹配的文本 "DELETE ALL FILES"');
            return false;
        }

        $this->newLine();
        $this->warn('⏳ 确认完成，将在 3 秒后开始删除操作...');
        $this->warn('按 Ctrl+C 可以取消操作');

        // 给用户最后 3 秒的反悔时间
        for ($i = 3; $i > 0; $i--) {
            $this->warn("倒计时: {$i} 秒...");
            sleep(1);
        }

        $this->newLine();
        $this->info('开始执行强制删除操作...');

        return true;
    }
}
