[2025-08-22 00:00:39] production.INFO: 清理服务器重启后的WebSocket会话 {"session_id":"ws_fWCY71u1yJDwPEL8cnK7WdvJQBJXYT1D","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","reason":"WebSocket服务器重启"} 
[2025-08-22 00:00:39] production.INFO: WebSocket服务器重启缓存清理完成 {"total_checked":1,"cleaned_count":1,"timestamp":"2025-08-22T00:00:39+08:00"} 
[2025-08-22 00:00:53] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:00:53] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:00:53] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:00:53] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:00:53] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:00:58] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:00:58] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:00:58] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_O3NaYafKokl2pFSq311SlbOo6R49zQ0h"} 
[2025-08-22 00:00:58] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:00:58] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:00:58] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:00:59] production.INFO: WebSocket连接认证成功 {"session_id":"ws_O3NaYafKokl2pFSq311SlbOo6R49zQ0h","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:01:34] production.INFO: WebSocket会话正常连接 {"session_id":"ws_O3NaYafKokl2pFSq311SlbOo6R49zQ0h","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:02:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":933,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:02:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:02:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":933,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755792173.20588,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:02:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":113.19,"total_stack_cleared":true} 
[2025-08-22 00:02:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:02:53+08:00"} 
[2025-08-22 00:04:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":933,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:04:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:04:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":933,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755792293.157313,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:04:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":70.88,"total_stack_cleared":true} 
[2025-08-22 00:04:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:04:53+08:00"} 
[2025-08-22 00:04:59] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:05:19] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:05:19] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:05:19] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_LZ1GAMZUWJu7Lh7327HZokmo4w9KdXPc"} 
[2025-08-22 00:05:19] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:05:19] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:05:19] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:05:19] production.INFO: WebSocket连接认证成功 {"session_id":"ws_LZ1GAMZUWJu7Lh7327HZokmo4w9KdXPc","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:05:34] production.INFO: WebSocket会话正常连接 {"session_id":"ws_LZ1GAMZUWJu7Lh7327HZokmo4w9KdXPc","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:05:45] production.WARNING: WebSocket连接错误 {"fd":3,"code":409,"message":"Session已在其他地方连接","error_message":{"type":"error","code":409,"message":"Session已在其他地方连接","data":{"timestamp":"2025-08-22T00:05:45+08:00","reason":"状态冲突","action":"请检查当前状态是否允许此操作"}}} 
[2025-08-22 00:06:08] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 00:06:08] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:06:08] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_55humLv8cu0gomxwgd76t2iPwYQWaJmA"} 
[2025-08-22 00:06:08] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_LZ1GAMZUWJu7Lh7327HZokmo4w9KdXPc","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:05:34+08:00"}]} 
[2025-08-22 00:06:08] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:06:08] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:06:08] production.INFO: WebSocket连接认证成功 {"session_id":"ws_55humLv8cu0gomxwgd76t2iPwYQWaJmA","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:06:55] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:12:02] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:12:02] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:12:02] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:12:02] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:12:02] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:12:16] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:12:16] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:12:16] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_EKwXlVS52bJEa9tK1P9N7XfXRynPGWyT"} 
[2025-08-22 00:12:16] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:12:16] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:12:16] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:12:16] production.INFO: WebSocket连接认证成功 {"session_id":"ws_EKwXlVS52bJEa9tK1P9N7XfXRynPGWyT","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:12:32] production.INFO: WebSocket会话正常连接 {"session_id":"ws_EKwXlVS52bJEa9tK1P9N7XfXRynPGWyT","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:12:46] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:12:57] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:12:57] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:12:57] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_n0tzVVNjvDGwbj0h4CkBUhw7hyVr1cnt"} 
[2025-08-22 00:12:57] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:12:57] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:12:57] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:12:57] production.INFO: WebSocket连接认证成功 {"session_id":"ws_n0tzVVNjvDGwbj0h4CkBUhw7hyVr1cnt","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:13:00] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:13:00] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:13:00] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_TmEB2nVHBWln6GrmVqkhZUokF3pVZeTm"} 
[2025-08-22 00:13:00] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:13:00] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:13:00] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:13:00] production.INFO: WebSocket连接认证成功 {"session_id":"ws_TmEB2nVHBWln6GrmVqkhZUokF3pVZeTm","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:13:12] production.INFO: WebSocket会话正常连接 {"session_id":"ws_TmEB2nVHBWln6GrmVqkhZUokF3pVZeTm","user_id":14,"fd":3,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:14:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:14:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:14:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755792842.87383,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:14:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":55.13,"total_stack_cleared":true} 
[2025-08-22 00:14:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:14:02+08:00"} 
[2025-08-22 00:16:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:16:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:16:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755792962.838873,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:16:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":114.97,"total_stack_cleared":true} 
[2025-08-22 00:16:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:16:02+08:00"} 
[2025-08-22 00:18:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:18:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:18:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755793082.848635,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:18:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":90.74,"total_stack_cleared":true} 
[2025-08-22 00:18:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:18:02+08:00"} 
[2025-08-22 00:20:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:20:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:20:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755793202.848492,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:20:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":76.41,"total_stack_cleared":true} 
[2025-08-22 00:20:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:20:02+08:00"} 
[2025-08-22 00:22:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:22:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:22:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755793322.845461,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:22:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":93.01,"total_stack_cleared":true} 
[2025-08-22 00:22:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:22:02+08:00"} 
[2025-08-22 00:23:36] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:23:36] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:23:36] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:23:36] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:23:36] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:24:04] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 00:24:04] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:24:04] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK"} 
[2025-08-22 00:24:04] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_TmEB2nVHBWln6GrmVqkhZUokF3pVZeTm","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:13:12+08:00"}]} 
[2025-08-22 00:24:04] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:24:04] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:24:04] production.INFO: WebSocket连接认证成功 {"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:24:18] production.INFO: WebSocket会话正常连接 {"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:24:50] production.INFO: WebSocket会话重连 {"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK","user_id":14,"fd":2,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-22 00:28:34] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:28:34] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:28:34] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:28:34] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:28:34] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:28:48] production.INFO: WebSocket会话重连 {"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK","user_id":14,"fd":1,"existing_fd":"2","reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-22 00:28:59] production.WARNING: WebSocket连接错误 {"fd":2,"code":409,"message":"会话已在其他地方连接","error_message":{"type":"error","code":409,"message":"会话已在其他地方连接","data":{"timestamp":"2025-08-22T00:28:59+08:00","reason":"状态冲突","action":"请检查当前状态是否允许此操作"}}} 
[2025-08-22 00:29:25] production.WARNING: WebSocket连接错误 {"fd":3,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T00:29:24+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 00:30:01] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:30:15] production.WARNING: WebSocket连接错误 {"fd":4,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T00:30:15+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 00:32:00] production.WARNING: WebSocket连接错误 {"fd":5,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T00:32:00+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 00:34:28] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:34:28] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:34:28] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:34:28] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:34:28] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:35:42] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:35:42] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:35:42] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_p4tqe7NUfVJTk0kCTEHF4mucmLFv6PFE"} 
[2025-08-22 00:35:42] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:35:42] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:35:42] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:35:42] production.INFO: WebSocket连接认证成功 {"session_id":"ws_p4tqe7NUfVJTk0kCTEHF4mucmLFv6PFE","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:35:54] production.INFO: WebSocket会话正常连接 {"session_id":"ws_p4tqe7NUfVJTk0kCTEHF4mucmLFv6PFE","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:36:28] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1435,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:36:28] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:36:28] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1435,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755794188.622026,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:36:28] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":129.09,"total_stack_cleared":true} 
[2025-08-22 00:36:28] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:36:28+08:00"} 
[2025-08-22 00:37:05] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:37:05] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:37:05] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:37:05] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:37:05] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:37:40] production.WARNING: WebSocket连接错误 {"fd":1,"code":409,"message":"会话已在其他地方连接","error_message":{"type":"error","code":409,"message":"会话已在其他地方连接","data":{"timestamp":"2025-08-22T00:37:40+08:00","reason":"状态冲突","action":"请检查当前状态是否允许此操作"}}} 
[2025-08-22 00:39:59] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:39:59] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:39:59] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_42bljnoWsdu0z39iKKcUN7WKiUAHkmGD"} 
[2025-08-22 00:39:59] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:39:59] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:39:59] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:39:59] production.INFO: WebSocket连接认证成功 {"session_id":"ws_42bljnoWsdu0z39iKKcUN7WKiUAHkmGD","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:40:11] production.INFO: WebSocket会话正常连接 {"session_id":"ws_42bljnoWsdu0z39iKKcUN7WKiUAHkmGD","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:40:19] production.WARNING: WebSocket连接错误 {"fd":3,"code":409,"message":"会话已在其他地方连接","error_message":{"type":"error","code":409,"message":"会话已在其他地方连接","data":{"timestamp":"2025-08-22T00:40:19+08:00","reason":"状态冲突","action":"请检查当前状态是否允许此操作"}}} 
[2025-08-22 00:40:47] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 00:40:47] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:40:47] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_AorkHVVQZL3aSA8GxZNf0qcSH8muXsM2"} 
[2025-08-22 00:40:47] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_42bljnoWsdu0z39iKKcUN7WKiUAHkmGD","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:40:11+08:00"}]} 
[2025-08-22 00:40:47] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:40:47] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:40:47] production.INFO: WebSocket连接认证成功 {"session_id":"ws_AorkHVVQZL3aSA8GxZNf0qcSH8muXsM2","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:41:02] production.INFO: WebSocket会话正常连接 {"session_id":"ws_AorkHVVQZL3aSA8GxZNf0qcSH8muXsM2","user_id":14,"fd":4,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:41:27] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:55:11] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:55:11] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:55:11] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO"} 
[2025-08-22 00:55:11] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:55:11] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:55:11] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:55:11] production.INFO: WebSocket连接认证成功 {"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:55:13] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:55:13] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:55:13] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:55:13] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:55:13] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:55:29] production.INFO: WebSocket会话正常连接 {"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:55:52] production.WARNING: WebSocket连接错误 {"fd":2,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T00:55:52+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 00:56:47] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 00:56:47] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:56:47] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_xSp8CwpHwb9Mzcn5BWwGRMO4uMKB8Ekz"} 
[2025-08-22 00:56:47] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:55:29+08:00"}]} 
[2025-08-22 00:56:47] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:56:47] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:56:47] production.INFO: WebSocket连接认证成功 {"session_id":"ws_xSp8CwpHwb9Mzcn5BWwGRMO4uMKB8Ekz","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:57:09] production.INFO: WebSocket会话正常连接 {"session_id":"ws_xSp8CwpHwb9Mzcn5BWwGRMO4uMKB8Ekz","user_id":14,"fd":3,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:57:13] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_AorkHVVQZL3aSA8GxZNf0qcSH8muXsM2","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","cached_fd":"4","reason":"Swoole自动断开无响应连接"} 
[2025-08-22 00:57:13] production.INFO: WebSocket会话状态清理完成 {"total_checked":3,"cleaned_count":1,"timestamp":"2025-08-22T00:57:13+08:00"} 
[2025-08-22 00:57:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:57:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:57:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795433.670999,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:57:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":13.68,"total_stack_cleared":true} 
[2025-08-22 00:57:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:57:13+08:00"} 
[2025-08-22 00:59:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:59:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:59:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795553.58049,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:59:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":99.79,"total_stack_cleared":true} 
[2025-08-22 00:59:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T00:59:13+08:00"} 
[2025-08-22 01:01:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:01:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:01:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795673.579456,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:01:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":104.77,"total_stack_cleared":true} 
[2025-08-22 01:01:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:01:13+08:00"} 
[2025-08-22 01:03:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:03:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:03:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795793.570631,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:03:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":104.72,"total_stack_cleared":true} 
[2025-08-22 01:03:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:03:13+08:00"} 
[2025-08-22 01:05:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:05:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:05:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795913.614401,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:05:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":97.97,"total_stack_cleared":true} 
[2025-08-22 01:05:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:05:13+08:00"} 
[2025-08-22 01:07:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:07:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:07:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796033.576813,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:07:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":83.81,"total_stack_cleared":true} 
[2025-08-22 01:07:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:07:13+08:00"} 
[2025-08-22 01:09:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:09:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:09:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796153.604256,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:09:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":106.77,"total_stack_cleared":true} 
[2025-08-22 01:09:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:09:13+08:00"} 
[2025-08-22 01:11:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:11:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:11:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796273.573115,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:11:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":39.66,"total_stack_cleared":true} 
[2025-08-22 01:11:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:11:13+08:00"} 
[2025-08-22 01:13:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:13:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:13:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796393.624867,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:13:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":103.57,"total_stack_cleared":true} 
[2025-08-22 01:13:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:13:13+08:00"} 
[2025-08-22 01:15:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:15:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:15:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796513.560953,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:15:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":77.35,"total_stack_cleared":true} 
[2025-08-22 01:15:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:15:13+08:00"} 
[2025-08-22 01:17:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:17:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:17:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796633.620437,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:17:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":35.76,"total_stack_cleared":true} 
[2025-08-22 01:17:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:17:13+08:00"} 
[2025-08-22 01:19:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:19:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:19:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796753.55354,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:19:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":115.39,"total_stack_cleared":true} 
[2025-08-22 01:19:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:19:13+08:00"} 
[2025-08-22 01:21:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:21:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:21:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796873.589585,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:21:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":50.18,"total_stack_cleared":true} 
[2025-08-22 01:21:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:21:13+08:00"} 
[2025-08-22 01:23:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:23:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:23:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796993.557293,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:23:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":41.03,"total_stack_cleared":true} 
[2025-08-22 01:23:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:23:13+08:00"} 
[2025-08-22 01:25:13] production.INFO: 从指定WebSocket会话获取business_type {"task_id":"text_gen_1755797113_UVZcYyv7","user_id":14,"session_id":"ws_xSp8CwpHwb9Mzcn5BWwGRMO4uMKB8Ekz","business_type":"text_generation_aistory"} 
[2025-08-22 01:25:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:25:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:25:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797113.583389,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:25:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":96.48,"total_stack_cleared":true} 
[2025-08-22 01:25:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:25:13+08:00"} 
[2025-08-22 01:25:27] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:26:15] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:26:15] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:26:15] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_0t92KfSBMqOovjW9ZxMIGRMzIzjtva7W"} 
[2025-08-22 01:26:15] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:55:29+08:00"}]} 
[2025-08-22 01:26:15] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:26:15] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:26:16] production.INFO: WebSocket连接认证成功 {"session_id":"ws_0t92KfSBMqOovjW9ZxMIGRMzIzjtva7W","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:27:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:27:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:27:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797233.566814,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:27:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":32.3,"total_stack_cleared":true} 
[2025-08-22 01:27:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T01:27:13+08:00"} 
[2025-08-22 01:28:24] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:28:24] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:28:24] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_Jpuw56pnJZbVZqWqCd7FkoUMfzgMuaEy"} 
[2025-08-22 01:28:24] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:55:29+08:00"}]} 
[2025-08-22 01:28:24] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:28:24] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:28:24] production.INFO: WebSocket连接认证成功 {"session_id":"ws_Jpuw56pnJZbVZqWqCd7FkoUMfzgMuaEy","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:29:09] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:34:11] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 01:34:11] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 01:34:11] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 01:34:11] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 01:34:11] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 01:34:12] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 01:34:12] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:34:12] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_XHd5RWI3rZ7uwJpmVssPek6VuwSWpzyV"} 
[2025-08-22 01:34:12] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 01:34:12] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:34:12] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:34:12] production.INFO: WebSocket连接认证成功 {"session_id":"ws_XHd5RWI3rZ7uwJpmVssPek6VuwSWpzyV","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:34:28] production.INFO: WebSocket会话正常连接 {"session_id":"ws_XHd5RWI3rZ7uwJpmVssPek6VuwSWpzyV","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:35:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:35:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:35:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797711.909641,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:35:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":65.75,"total_stack_cleared":true} 
[2025-08-22 01:35:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T01:35:11+08:00"} 
[2025-08-22 01:35:13] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:35:16] production.WARNING: WebSocket连接错误 {"fd":2,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T01:35:15+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 01:35:27] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 01:35:27] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:35:27] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ"} 
[2025-08-22 01:35:27] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 01:35:27] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:35:27] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:35:27] production.INFO: WebSocket连接认证成功 {"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:35:41] production.INFO: WebSocket会话正常连接 {"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","user_id":14,"fd":3,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:36:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:36:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:36:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797771.877447,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:36:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":80.27,"total_stack_cleared":true} 
[2025-08-22 01:36:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T01:36:11+08:00"} 
[2025-08-22 01:36:14] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:36:14] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:36:14] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_Ns7C9SgtKIw6o7TTmA5tEqaD7z0Nodwn"} 
[2025-08-22 01:36:14] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:35:41+08:00"}]} 
[2025-08-22 01:36:14] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:36:14] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:36:14] production.INFO: WebSocket连接认证成功 {"session_id":"ws_Ns7C9SgtKIw6o7TTmA5tEqaD7z0Nodwn","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:36:32] production.INFO: WebSocket会话正常连接 {"session_id":"ws_Ns7C9SgtKIw6o7TTmA5tEqaD7z0Nodwn","user_id":14,"fd":4,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:37:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:37:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:37:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797831.898849,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:37:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":75.06,"total_stack_cleared":true} 
[2025-08-22 01:37:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:37:11+08:00"} 
[2025-08-22 01:37:25] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:38:08] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:38:08] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:38:08] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX"} 
[2025-08-22 01:38:08] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:35:41+08:00"}]} 
[2025-08-22 01:38:08] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:38:08] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:38:08] production.INFO: WebSocket连接认证成功 {"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:38:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:38:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:38:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797891.867278,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:38:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":8.18,"total_stack_cleared":true} 
[2025-08-22 01:38:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T01:38:11+08:00"} 
[2025-08-22 01:38:19] production.INFO: WebSocket会话正常连接 {"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","user_id":14,"fd":5,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:39:03] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:39:03] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:39:03] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL"} 
[2025-08-22 01:39:03] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:35:41+08:00"},{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"}]} 
[2025-08-22 01:39:03] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:39:03] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":0,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:39:03] production.INFO: WebSocket连接认证成功 {"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:39:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:39:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:39:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797951.910297,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:39:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":25.89,"total_stack_cleared":true} 
[2025-08-22 01:39:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:39:11+08:00"} 
[2025-08-22 01:39:22] production.INFO: WebSocket会话正常连接 {"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","user_id":14,"fd":6,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:40:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:40:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:40:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798011.878721,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:40:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":58.28,"total_stack_cleared":true} 
[2025-08-22 01:40:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":3,"timestamp":"2025-08-22T01:40:11+08:00"} 
[2025-08-22 01:41:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:41:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:41:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798071.899884,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:41:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":15.18,"total_stack_cleared":true} 
[2025-08-22 01:41:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":3,"timestamp":"2025-08-22T01:41:11+08:00"} 
[2025-08-22 01:41:14] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":3,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:41:14] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":3,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:41:14] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l"} 
[2025-08-22 01:41:14] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":3,"existing_sessions":[{"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:35:41+08:00"},{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:39:22+08:00"}]} 
[2025-08-22 01:41:14] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:41:14] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"existing_different_type_connections":0,"total_existing_connections":3,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:41:14] production.INFO: WebSocket连接认证成功 {"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:41:25] production.INFO: WebSocket会话正常连接 {"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","user_id":14,"fd":7,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:41:45] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:42:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:42:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:42:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798131.879279,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:42:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":31.91,"total_stack_cleared":true} 
[2025-08-22 01:42:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":3,"timestamp":"2025-08-22T01:42:11+08:00"} 
[2025-08-22 01:42:12] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":3,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:42:12] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":3,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:42:12] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_q6n7mez9wuDEP55pjCtpHVjK7lfSZGfL"} 
[2025-08-22 01:42:12] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":3,"existing_sessions":[{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:39:22+08:00"},{"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:41:25+08:00"}]} 
[2025-08-22 01:42:12] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:42:12] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"existing_different_type_connections":0,"total_existing_connections":3,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:42:12] production.INFO: WebSocket连接认证成功 {"session_id":"ws_q6n7mez9wuDEP55pjCtpHVjK7lfSZGfL","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:42:38] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":3,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:42:38] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":3,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:42:38] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_A4vUP7XMjFKga209OXcHTWIcd3c0pNSK"} 
[2025-08-22 01:42:38] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":3,"existing_sessions":[{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:39:22+08:00"},{"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:41:25+08:00"}]} 
[2025-08-22 01:42:38] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:42:38] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"existing_different_type_connections":0,"total_existing_connections":3,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:42:39] production.INFO: WebSocket连接认证成功 {"session_id":"ws_A4vUP7XMjFKga209OXcHTWIcd3c0pNSK","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:43:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:43:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:43:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798191.911063,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:43:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":18.45,"total_stack_cleared":true} 
[2025-08-22 01:43:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":3,"timestamp":"2025-08-22T01:43:11+08:00"} 
[2025-08-22 01:44:05] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:44:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:44:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:44:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798251.895993,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:44:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":25.71,"total_stack_cleared":true} 
[2025-08-22 01:44:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:44:11+08:00"} 
[2025-08-22 01:44:24] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:44:24] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:44:24] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_GTaV31J4zBGIxbEbfBGrr0yexNi1ue5L"} 
[2025-08-22 01:44:24] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:41:25+08:00"}]} 
[2025-08-22 01:44:24] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:44:24] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":0,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:44:24] production.INFO: WebSocket连接认证成功 {"session_id":"ws_GTaV31J4zBGIxbEbfBGrr0yexNi1ue5L","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
