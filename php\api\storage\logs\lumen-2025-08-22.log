[2025-08-22 00:00:39] production.INFO: 清理服务器重启后的WebSocket会话 {"session_id":"ws_fWCY71u1yJDwPEL8cnK7WdvJQBJXYT1D","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","reason":"WebSocket服务器重启"} 
[2025-08-22 00:00:39] production.INFO: WebSocket服务器重启缓存清理完成 {"total_checked":1,"cleaned_count":1,"timestamp":"2025-08-22T00:00:39+08:00"} 
[2025-08-22 00:00:53] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:00:53] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:00:53] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:00:53] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:00:53] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:00:58] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:00:58] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:00:58] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_O3NaYafKokl2pFSq311SlbOo6R49zQ0h"} 
[2025-08-22 00:00:58] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:00:58] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:00:58] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:00:59] production.INFO: WebSocket连接认证成功 {"session_id":"ws_O3NaYafKokl2pFSq311SlbOo6R49zQ0h","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:01:34] production.INFO: WebSocket会话正常连接 {"session_id":"ws_O3NaYafKokl2pFSq311SlbOo6R49zQ0h","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:02:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":933,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:02:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:02:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":933,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755792173.20588,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:02:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":113.19,"total_stack_cleared":true} 
[2025-08-22 00:02:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:02:53+08:00"} 
[2025-08-22 00:04:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":933,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:04:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:04:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":933,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755792293.157313,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:04:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":70.88,"total_stack_cleared":true} 
[2025-08-22 00:04:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:04:53+08:00"} 
[2025-08-22 00:04:59] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:05:19] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:05:19] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:05:19] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_LZ1GAMZUWJu7Lh7327HZokmo4w9KdXPc"} 
[2025-08-22 00:05:19] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:05:19] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:05:19] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:05:19] production.INFO: WebSocket连接认证成功 {"session_id":"ws_LZ1GAMZUWJu7Lh7327HZokmo4w9KdXPc","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:05:34] production.INFO: WebSocket会话正常连接 {"session_id":"ws_LZ1GAMZUWJu7Lh7327HZokmo4w9KdXPc","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:05:45] production.WARNING: WebSocket连接错误 {"fd":3,"code":409,"message":"Session已在其他地方连接","error_message":{"type":"error","code":409,"message":"Session已在其他地方连接","data":{"timestamp":"2025-08-22T00:05:45+08:00","reason":"状态冲突","action":"请检查当前状态是否允许此操作"}}} 
[2025-08-22 00:06:08] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 00:06:08] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:06:08] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_55humLv8cu0gomxwgd76t2iPwYQWaJmA"} 
[2025-08-22 00:06:08] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_LZ1GAMZUWJu7Lh7327HZokmo4w9KdXPc","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:05:34+08:00"}]} 
[2025-08-22 00:06:08] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:06:08] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:06:08] production.INFO: WebSocket连接认证成功 {"session_id":"ws_55humLv8cu0gomxwgd76t2iPwYQWaJmA","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:06:55] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:12:02] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:12:02] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:12:02] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:12:02] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:12:02] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:12:16] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:12:16] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:12:16] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_EKwXlVS52bJEa9tK1P9N7XfXRynPGWyT"} 
[2025-08-22 00:12:16] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:12:16] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:12:16] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:12:16] production.INFO: WebSocket连接认证成功 {"session_id":"ws_EKwXlVS52bJEa9tK1P9N7XfXRynPGWyT","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:12:32] production.INFO: WebSocket会话正常连接 {"session_id":"ws_EKwXlVS52bJEa9tK1P9N7XfXRynPGWyT","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:12:46] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:12:57] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:12:57] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:12:57] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_n0tzVVNjvDGwbj0h4CkBUhw7hyVr1cnt"} 
[2025-08-22 00:12:57] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:12:57] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:12:57] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:12:57] production.INFO: WebSocket连接认证成功 {"session_id":"ws_n0tzVVNjvDGwbj0h4CkBUhw7hyVr1cnt","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:13:00] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:13:00] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:13:00] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_TmEB2nVHBWln6GrmVqkhZUokF3pVZeTm"} 
[2025-08-22 00:13:00] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:13:00] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:13:00] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:13:00] production.INFO: WebSocket连接认证成功 {"session_id":"ws_TmEB2nVHBWln6GrmVqkhZUokF3pVZeTm","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:13:12] production.INFO: WebSocket会话正常连接 {"session_id":"ws_TmEB2nVHBWln6GrmVqkhZUokF3pVZeTm","user_id":14,"fd":3,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:14:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:14:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:14:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755792842.87383,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:14:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":55.13,"total_stack_cleared":true} 
[2025-08-22 00:14:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:14:02+08:00"} 
[2025-08-22 00:16:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:16:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:16:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755792962.838873,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:16:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":114.97,"total_stack_cleared":true} 
[2025-08-22 00:16:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:16:02+08:00"} 
[2025-08-22 00:18:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:18:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:18:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755793082.848635,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:18:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":90.74,"total_stack_cleared":true} 
[2025-08-22 00:18:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:18:02+08:00"} 
[2025-08-22 00:20:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:20:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:20:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755793202.848492,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:20:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":76.41,"total_stack_cleared":true} 
[2025-08-22 00:20:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:20:02+08:00"} 
[2025-08-22 00:22:02] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1652,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:22:02] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:22:02] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1652,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755793322.845461,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":290,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:22:02] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":93.01,"total_stack_cleared":true} 
[2025-08-22 00:22:02] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:22:02+08:00"} 
[2025-08-22 00:23:36] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:23:36] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:23:36] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:23:36] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:23:36] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:24:04] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 00:24:04] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:24:04] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK"} 
[2025-08-22 00:24:04] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_TmEB2nVHBWln6GrmVqkhZUokF3pVZeTm","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:13:12+08:00"}]} 
[2025-08-22 00:24:04] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:24:04] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:24:04] production.INFO: WebSocket连接认证成功 {"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:24:18] production.INFO: WebSocket会话正常连接 {"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:24:50] production.INFO: WebSocket会话重连 {"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK","user_id":14,"fd":2,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-22 00:28:34] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:28:34] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:28:34] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:28:34] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:28:34] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:28:48] production.INFO: WebSocket会话重连 {"session_id":"ws_bqlvsSGWo0FS21BYpX8GzOFkE28OT2eK","user_id":14,"fd":1,"existing_fd":"2","reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-22 00:28:59] production.WARNING: WebSocket连接错误 {"fd":2,"code":409,"message":"会话已在其他地方连接","error_message":{"type":"error","code":409,"message":"会话已在其他地方连接","data":{"timestamp":"2025-08-22T00:28:59+08:00","reason":"状态冲突","action":"请检查当前状态是否允许此操作"}}} 
[2025-08-22 00:29:25] production.WARNING: WebSocket连接错误 {"fd":3,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T00:29:24+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 00:30:01] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:30:15] production.WARNING: WebSocket连接错误 {"fd":4,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T00:30:15+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 00:32:00] production.WARNING: WebSocket连接错误 {"fd":5,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T00:32:00+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 00:34:28] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:34:28] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:34:28] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:34:28] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:34:28] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:35:42] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:35:42] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:35:42] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_p4tqe7NUfVJTk0kCTEHF4mucmLFv6PFE"} 
[2025-08-22 00:35:42] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:35:42] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:35:42] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:35:42] production.INFO: WebSocket连接认证成功 {"session_id":"ws_p4tqe7NUfVJTk0kCTEHF4mucmLFv6PFE","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:35:54] production.INFO: WebSocket会话正常连接 {"session_id":"ws_p4tqe7NUfVJTk0kCTEHF4mucmLFv6PFE","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:36:28] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1435,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:36:28] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:36:28] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1435,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755794188.622026,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:36:28] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":129.09,"total_stack_cleared":true} 
[2025-08-22 00:36:28] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:36:28+08:00"} 
[2025-08-22 00:37:05] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:37:05] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:37:05] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:37:05] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:37:05] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:37:40] production.WARNING: WebSocket连接错误 {"fd":1,"code":409,"message":"会话已在其他地方连接","error_message":{"type":"error","code":409,"message":"会话已在其他地方连接","data":{"timestamp":"2025-08-22T00:37:40+08:00","reason":"状态冲突","action":"请检查当前状态是否允许此操作"}}} 
[2025-08-22 00:39:59] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:39:59] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:39:59] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_42bljnoWsdu0z39iKKcUN7WKiUAHkmGD"} 
[2025-08-22 00:39:59] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:39:59] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:39:59] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:39:59] production.INFO: WebSocket连接认证成功 {"session_id":"ws_42bljnoWsdu0z39iKKcUN7WKiUAHkmGD","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:40:11] production.INFO: WebSocket会话正常连接 {"session_id":"ws_42bljnoWsdu0z39iKKcUN7WKiUAHkmGD","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:40:19] production.WARNING: WebSocket连接错误 {"fd":3,"code":409,"message":"会话已在其他地方连接","error_message":{"type":"error","code":409,"message":"会话已在其他地方连接","data":{"timestamp":"2025-08-22T00:40:19+08:00","reason":"状态冲突","action":"请检查当前状态是否允许此操作"}}} 
[2025-08-22 00:40:47] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 00:40:47] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:40:47] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_AorkHVVQZL3aSA8GxZNf0qcSH8muXsM2"} 
[2025-08-22 00:40:47] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_42bljnoWsdu0z39iKKcUN7WKiUAHkmGD","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:40:11+08:00"}]} 
[2025-08-22 00:40:47] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:40:47] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:40:47] production.INFO: WebSocket连接认证成功 {"session_id":"ws_AorkHVVQZL3aSA8GxZNf0qcSH8muXsM2","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:41:02] production.INFO: WebSocket会话正常连接 {"session_id":"ws_AorkHVVQZL3aSA8GxZNf0qcSH8muXsM2","user_id":14,"fd":4,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:41:27] production.INFO: User offline {"user_id":14} 
[2025-08-22 00:55:11] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 00:55:11] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:55:11] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO"} 
[2025-08-22 00:55:11] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 00:55:11] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:55:11] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:55:11] production.INFO: WebSocket连接认证成功 {"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:55:13] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 00:55:13] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 00:55:13] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 00:55:13] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 00:55:13] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 00:55:29] production.INFO: WebSocket会话正常连接 {"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:55:52] production.WARNING: WebSocket连接错误 {"fd":2,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T00:55:52+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 00:56:47] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 00:56:47] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 00:56:47] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_xSp8CwpHwb9Mzcn5BWwGRMO4uMKB8Ekz"} 
[2025-08-22 00:56:47] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:55:29+08:00"}]} 
[2025-08-22 00:56:47] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 00:56:47] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 00:56:47] production.INFO: WebSocket连接认证成功 {"session_id":"ws_xSp8CwpHwb9Mzcn5BWwGRMO4uMKB8Ekz","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 00:57:09] production.INFO: WebSocket会话正常连接 {"session_id":"ws_xSp8CwpHwb9Mzcn5BWwGRMO4uMKB8Ekz","user_id":14,"fd":3,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 00:57:13] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_AorkHVVQZL3aSA8GxZNf0qcSH8muXsM2","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","cached_fd":"4","reason":"Swoole自动断开无响应连接"} 
[2025-08-22 00:57:13] production.INFO: WebSocket会话状态清理完成 {"total_checked":3,"cleaned_count":1,"timestamp":"2025-08-22T00:57:13+08:00"} 
[2025-08-22 00:57:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:57:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:57:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795433.670999,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:57:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":13.68,"total_stack_cleared":true} 
[2025-08-22 00:57:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T00:57:13+08:00"} 
[2025-08-22 00:59:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:59:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 00:59:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795553.58049,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 00:59:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":99.79,"total_stack_cleared":true} 
[2025-08-22 00:59:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T00:59:13+08:00"} 
[2025-08-22 01:01:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:01:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:01:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795673.579456,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:01:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":104.77,"total_stack_cleared":true} 
[2025-08-22 01:01:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:01:13+08:00"} 
[2025-08-22 01:03:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:03:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:03:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795793.570631,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:03:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":104.72,"total_stack_cleared":true} 
[2025-08-22 01:03:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:03:13+08:00"} 
[2025-08-22 01:05:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:05:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:05:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755795913.614401,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:05:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":97.97,"total_stack_cleared":true} 
[2025-08-22 01:05:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:05:13+08:00"} 
[2025-08-22 01:07:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:07:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:07:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796033.576813,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:07:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":83.81,"total_stack_cleared":true} 
[2025-08-22 01:07:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:07:13+08:00"} 
[2025-08-22 01:09:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:09:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:09:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796153.604256,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:09:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":106.77,"total_stack_cleared":true} 
[2025-08-22 01:09:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:09:13+08:00"} 
[2025-08-22 01:11:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:11:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:11:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796273.573115,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:11:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":39.66,"total_stack_cleared":true} 
[2025-08-22 01:11:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:11:13+08:00"} 
[2025-08-22 01:13:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:13:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:13:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796393.624867,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:13:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":103.57,"total_stack_cleared":true} 
[2025-08-22 01:13:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:13:13+08:00"} 
[2025-08-22 01:15:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:15:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:15:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796513.560953,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:15:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":77.35,"total_stack_cleared":true} 
[2025-08-22 01:15:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:15:13+08:00"} 
[2025-08-22 01:17:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:17:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:17:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796633.620437,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:17:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":35.76,"total_stack_cleared":true} 
[2025-08-22 01:17:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:17:13+08:00"} 
[2025-08-22 01:19:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:19:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:19:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796753.55354,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:19:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":115.39,"total_stack_cleared":true} 
[2025-08-22 01:19:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:19:13+08:00"} 
[2025-08-22 01:21:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:21:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:21:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796873.589585,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:21:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":50.18,"total_stack_cleared":true} 
[2025-08-22 01:21:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:21:13+08:00"} 
[2025-08-22 01:23:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:23:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:23:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755796993.557293,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:23:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":41.03,"total_stack_cleared":true} 
[2025-08-22 01:23:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:23:13+08:00"} 
[2025-08-22 01:25:13] production.INFO: 从指定WebSocket会话获取business_type {"task_id":"text_gen_1755797113_UVZcYyv7","user_id":14,"session_id":"ws_xSp8CwpHwb9Mzcn5BWwGRMO4uMKB8Ekz","business_type":"text_generation_aistory"} 
[2025-08-22 01:25:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:25:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:25:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797113.583389,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:25:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":96.48,"total_stack_cleared":true} 
[2025-08-22 01:25:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:25:13+08:00"} 
[2025-08-22 01:25:27] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:26:15] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:26:15] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:26:15] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_0t92KfSBMqOovjW9ZxMIGRMzIzjtva7W"} 
[2025-08-22 01:26:15] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:55:29+08:00"}]} 
[2025-08-22 01:26:15] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:26:15] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:26:16] production.INFO: WebSocket连接认证成功 {"session_id":"ws_0t92KfSBMqOovjW9ZxMIGRMzIzjtva7W","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:27:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1047,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:27:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:27:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1047,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797233.566814,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":281,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:27:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":32.3,"total_stack_cleared":true} 
[2025-08-22 01:27:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T01:27:13+08:00"} 
[2025-08-22 01:28:24] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:28:24] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:28:24] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_Jpuw56pnJZbVZqWqCd7FkoUMfzgMuaEy"} 
[2025-08-22 01:28:24] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_57QRsdHf5QwNMZR6ZqztIYusT4WSSDPO","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T00:55:29+08:00"}]} 
[2025-08-22 01:28:24] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:28:24] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:28:24] production.INFO: WebSocket连接认证成功 {"session_id":"ws_Jpuw56pnJZbVZqWqCd7FkoUMfzgMuaEy","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:29:09] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:34:11] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 01:34:11] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 01:34:11] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 01:34:11] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 01:34:11] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 01:34:12] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 01:34:12] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:34:12] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_XHd5RWI3rZ7uwJpmVssPek6VuwSWpzyV"} 
[2025-08-22 01:34:12] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 01:34:12] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:34:12] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:34:12] production.INFO: WebSocket连接认证成功 {"session_id":"ws_XHd5RWI3rZ7uwJpmVssPek6VuwSWpzyV","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:34:28] production.INFO: WebSocket会话正常连接 {"session_id":"ws_XHd5RWI3rZ7uwJpmVssPek6VuwSWpzyV","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:35:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:35:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:35:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797711.909641,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:35:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":65.75,"total_stack_cleared":true} 
[2025-08-22 01:35:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T01:35:11+08:00"} 
[2025-08-22 01:35:13] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:35:16] production.WARNING: WebSocket连接错误 {"fd":2,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T01:35:15+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 01:35:27] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 01:35:27] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:35:27] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ"} 
[2025-08-22 01:35:27] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 01:35:27] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:35:27] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:35:27] production.INFO: WebSocket连接认证成功 {"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:35:41] production.INFO: WebSocket会话正常连接 {"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","user_id":14,"fd":3,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:36:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:36:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:36:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797771.877447,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:36:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":80.27,"total_stack_cleared":true} 
[2025-08-22 01:36:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T01:36:11+08:00"} 
[2025-08-22 01:36:14] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:36:14] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:36:14] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_Ns7C9SgtKIw6o7TTmA5tEqaD7z0Nodwn"} 
[2025-08-22 01:36:14] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:35:41+08:00"}]} 
[2025-08-22 01:36:14] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:36:14] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:36:14] production.INFO: WebSocket连接认证成功 {"session_id":"ws_Ns7C9SgtKIw6o7TTmA5tEqaD7z0Nodwn","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:36:32] production.INFO: WebSocket会话正常连接 {"session_id":"ws_Ns7C9SgtKIw6o7TTmA5tEqaD7z0Nodwn","user_id":14,"fd":4,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:37:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:37:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:37:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797831.898849,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:37:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":75.06,"total_stack_cleared":true} 
[2025-08-22 01:37:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:37:11+08:00"} 
[2025-08-22 01:37:25] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:38:08] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:38:08] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:38:08] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX"} 
[2025-08-22 01:38:08] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:35:41+08:00"}]} 
[2025-08-22 01:38:08] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:38:08] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:38:08] production.INFO: WebSocket连接认证成功 {"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:38:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:38:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:38:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797891.867278,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:38:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":8.18,"total_stack_cleared":true} 
[2025-08-22 01:38:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T01:38:11+08:00"} 
[2025-08-22 01:38:19] production.INFO: WebSocket会话正常连接 {"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","user_id":14,"fd":5,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:39:03] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:39:03] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:39:03] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL"} 
[2025-08-22 01:39:03] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:35:41+08:00"},{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"}]} 
[2025-08-22 01:39:03] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:39:03] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":0,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:39:03] production.INFO: WebSocket连接认证成功 {"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:39:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:39:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:39:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755797951.910297,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:39:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":25.89,"total_stack_cleared":true} 
[2025-08-22 01:39:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:39:11+08:00"} 
[2025-08-22 01:39:22] production.INFO: WebSocket会话正常连接 {"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","user_id":14,"fd":6,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:40:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:40:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:40:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798011.878721,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:40:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":58.28,"total_stack_cleared":true} 
[2025-08-22 01:40:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":3,"timestamp":"2025-08-22T01:40:11+08:00"} 
[2025-08-22 01:41:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:41:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:41:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798071.899884,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:41:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":15.18,"total_stack_cleared":true} 
[2025-08-22 01:41:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":3,"timestamp":"2025-08-22T01:41:11+08:00"} 
[2025-08-22 01:41:14] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":3,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:41:14] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":3,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:41:14] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l"} 
[2025-08-22 01:41:14] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":3,"existing_sessions":[{"session_id":"ws_hyXFBQStsQtwCssSmkzAQjPsmXt2eZBQ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:35:41+08:00"},{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:39:22+08:00"}]} 
[2025-08-22 01:41:14] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:41:14] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"existing_different_type_connections":0,"total_existing_connections":3,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:41:14] production.INFO: WebSocket连接认证成功 {"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:41:25] production.INFO: WebSocket会话正常连接 {"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","user_id":14,"fd":7,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 01:41:45] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:42:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:42:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:42:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798131.879279,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:42:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":31.91,"total_stack_cleared":true} 
[2025-08-22 01:42:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":3,"timestamp":"2025-08-22T01:42:11+08:00"} 
[2025-08-22 01:42:12] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":3,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:42:12] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":3,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:42:12] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_q6n7mez9wuDEP55pjCtpHVjK7lfSZGfL"} 
[2025-08-22 01:42:12] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":3,"existing_sessions":[{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:39:22+08:00"},{"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:41:25+08:00"}]} 
[2025-08-22 01:42:12] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:42:12] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"existing_different_type_connections":0,"total_existing_connections":3,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:42:12] production.INFO: WebSocket连接认证成功 {"session_id":"ws_q6n7mez9wuDEP55pjCtpHVjK7lfSZGfL","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:42:38] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":3,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:42:38] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":3,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:42:38] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_A4vUP7XMjFKga209OXcHTWIcd3c0pNSK"} 
[2025-08-22 01:42:38] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":3,"existing_sessions":[{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_7VjUqubN2nqHRz8jGf1MUH9Kwg6qhKvL","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:39:22+08:00"},{"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:41:25+08:00"}]} 
[2025-08-22 01:42:38] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:42:38] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":3,"existing_different_type_connections":0,"total_existing_connections":3,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:42:39] production.INFO: WebSocket连接认证成功 {"session_id":"ws_A4vUP7XMjFKga209OXcHTWIcd3c0pNSK","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:43:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:43:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:43:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798191.911063,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:43:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":18.45,"total_stack_cleared":true} 
[2025-08-22 01:43:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":3,"timestamp":"2025-08-22T01:43:11+08:00"} 
[2025-08-22 01:44:05] production.INFO: User offline {"user_id":14} 
[2025-08-22 01:44:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:44:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:44:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798251.895993,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:44:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":25.71,"total_stack_cleared":true} 
[2025-08-22 01:44:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:44:11+08:00"} 
[2025-08-22 01:44:24] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 01:44:24] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 01:44:24] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_GTaV31J4zBGIxbEbfBGrr0yexNi1ue5L"} 
[2025-08-22 01:44:24] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:41:25+08:00"}]} 
[2025-08-22 01:44:24] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 01:44:24] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":0,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 01:44:24] production.INFO: WebSocket连接认证成功 {"session_id":"ws_GTaV31J4zBGIxbEbfBGrr0yexNi1ue5L","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 01:45:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:45:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:45:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798311.912275,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:45:12] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":100.4,"total_stack_cleared":true} 
[2025-08-22 01:45:12] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:45:12+08:00"} 
[2025-08-22 01:46:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:46:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:46:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798371.880552,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:46:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":50.46,"total_stack_cleared":true} 
[2025-08-22 01:46:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:46:11+08:00"} 
[2025-08-22 01:47:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:47:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:47:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798431.927642,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:47:12] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":96.43,"total_stack_cleared":true} 
[2025-08-22 01:47:12] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:47:12+08:00"} 
[2025-08-22 01:48:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:48:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:48:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798491.887773,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:48:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":36.65,"total_stack_cleared":true} 
[2025-08-22 01:48:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:48:11+08:00"} 
[2025-08-22 01:49:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:49:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:49:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798551.904191,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:49:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":79.13,"total_stack_cleared":true} 
[2025-08-22 01:49:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:49:11+08:00"} 
[2025-08-22 01:50:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:50:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:50:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798611.875214,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:50:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":74.89,"total_stack_cleared":true} 
[2025-08-22 01:50:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:50:11+08:00"} 
[2025-08-22 01:51:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:51:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:51:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798671.937907,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:51:12] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":94.17,"total_stack_cleared":true} 
[2025-08-22 01:51:12] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:51:12+08:00"} 
[2025-08-22 01:52:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:52:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:52:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798731.865594,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:52:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":45.15,"total_stack_cleared":true} 
[2025-08-22 01:52:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:52:11+08:00"} 
[2025-08-22 01:53:11] production.INFO: pending状态会话超时 {"session_id":"ws_q6n7mez9wuDEP55pjCtpHVjK7lfSZGfL","user_id":14,"created_at":"2025-08-22 01:42:12","timeout_minutes":10,"reason":"pending状态超过10分钟未连接"} 
[2025-08-22 01:53:12] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_q6n7mez9wuDEP55pjCtpHVjK7lfSZGfL","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","original_status":"disconnected","cached_fd":null,"reason":"pending状态超时，认证后未及时建立连接"} 
[2025-08-22 01:53:12] production.INFO: pending状态会话超时 {"session_id":"ws_A4vUP7XMjFKga209OXcHTWIcd3c0pNSK","user_id":14,"created_at":"2025-08-22 01:42:38","timeout_minutes":10,"reason":"pending状态超过10分钟未连接"} 
[2025-08-22 01:53:12] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_A4vUP7XMjFKga209OXcHTWIcd3c0pNSK","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","original_status":"disconnected","cached_fd":null,"reason":"pending状态超时，认证后未及时建立连接"} 
[2025-08-22 01:53:12] production.INFO: WebSocket会话状态清理完成 {"total_checked":5,"cleaned_count":2,"checked_statuses":["connected","pending"],"timestamp":"2025-08-22T01:53:12+08:00"} 
[2025-08-22 01:53:12] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:53:12] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:53:12] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798792.079542,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:53:12] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":10.55,"total_stack_cleared":true} 
[2025-08-22 01:53:12] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:53:12+08:00"} 
[2025-08-22 01:54:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:54:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:54:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798851.896504,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:54:12] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":130.47,"total_stack_cleared":true} 
[2025-08-22 01:54:12] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:54:12+08:00"} 
[2025-08-22 01:55:11] production.INFO: pending状态会话超时 {"session_id":"ws_GTaV31J4zBGIxbEbfBGrr0yexNi1ue5L","user_id":14,"created_at":"2025-08-22 01:44:24","timeout_minutes":10,"reason":"pending状态超过10分钟未连接"} 
[2025-08-22 01:55:11] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_GTaV31J4zBGIxbEbfBGrr0yexNi1ue5L","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","original_status":"disconnected","cached_fd":null,"reason":"pending状态超时，认证后未及时建立连接"} 
[2025-08-22 01:55:11] production.INFO: WebSocket会话状态清理完成 {"total_checked":3,"cleaned_count":1,"checked_statuses":["connected","pending"],"timestamp":"2025-08-22T01:55:11+08:00"} 
[2025-08-22 01:55:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:55:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:55:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798911.945661,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:55:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":15.36,"total_stack_cleared":true} 
[2025-08-22 01:55:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:55:11+08:00"} 
[2025-08-22 01:56:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:56:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:56:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755798971.867,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:56:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":74.17,"total_stack_cleared":true} 
[2025-08-22 01:56:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:56:11+08:00"} 
[2025-08-22 01:57:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:57:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:57:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799031.911781,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:57:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":65.36,"total_stack_cleared":true} 
[2025-08-22 01:57:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:57:11+08:00"} 
[2025-08-22 01:58:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:58:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:58:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799091.867032,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:58:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":50.01,"total_stack_cleared":true} 
[2025-08-22 01:58:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:58:11+08:00"} 
[2025-08-22 01:59:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:59:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 01:59:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799151.892866,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 01:59:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":22.32,"total_stack_cleared":true} 
[2025-08-22 01:59:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T01:59:11+08:00"} 
[2025-08-22 02:00:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:00:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:00:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799211.866345,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:00:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":41.06,"total_stack_cleared":true} 
[2025-08-22 02:00:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:00:11+08:00"} 
[2025-08-22 02:01:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:01:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:01:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799271.884794,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:01:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":25.63,"total_stack_cleared":true} 
[2025-08-22 02:01:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:01:11+08:00"} 
[2025-08-22 02:02:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:02:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:02:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799331.883004,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:02:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":19.46,"total_stack_cleared":true} 
[2025-08-22 02:02:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:02:11+08:00"} 
[2025-08-22 02:03:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:03:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:03:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799391.896336,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:03:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":25.94,"total_stack_cleared":true} 
[2025-08-22 02:03:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:03:11+08:00"} 
[2025-08-22 02:04:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:04:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:04:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799451.872636,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:04:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":61.83,"total_stack_cleared":true} 
[2025-08-22 02:04:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:04:11+08:00"} 
[2025-08-22 02:05:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:05:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:05:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799511.879551,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:05:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":93.26,"total_stack_cleared":true} 
[2025-08-22 02:05:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:05:11+08:00"} 
[2025-08-22 02:06:06] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 02:06:06] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 02:06:06] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_x31W6QOM22vKYHo3lr2CHYdw5pwpar9C"} 
[2025-08-22 02:06:06] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:41:25+08:00"}]} 
[2025-08-22 02:06:06] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:06:06] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":0,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:06:06] production.INFO: WebSocket连接认证成功 {"session_id":"ws_x31W6QOM22vKYHo3lr2CHYdw5pwpar9C","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 02:06:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:06:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:06:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799571.875287,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:06:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":14.67,"total_stack_cleared":true} 
[2025-08-22 02:06:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:06:11+08:00"} 
[2025-08-22 02:06:51] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 02:06:51] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 02:06:51] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_PNbR3X0xhap77rHQTQxuWuYKtZ3tWQ97"} 
[2025-08-22 02:06:51] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:41:25+08:00"}]} 
[2025-08-22 02:06:51] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:06:51] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":0,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:06:51] production.INFO: WebSocket连接认证成功 {"session_id":"ws_PNbR3X0xhap77rHQTQxuWuYKtZ3tWQ97","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 02:07:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:07:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:07:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799631.898711,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:07:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":37.27,"total_stack_cleared":true} 
[2025-08-22 02:07:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:07:11+08:00"} 
[2025-08-22 02:08:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:08:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:08:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799691.875962,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:08:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":92.97,"total_stack_cleared":true} 
[2025-08-22 02:08:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:08:11+08:00"} 
[2025-08-22 02:09:11] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":795,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:09:11] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:09:11] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":795,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799751.899791,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:09:11] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":55.56,"total_stack_cleared":true} 
[2025-08-22 02:09:11] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:09:11+08:00"} 
[2025-08-22 02:09:42] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 02:09:42] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 02:09:42] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 02:09:42] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 02:09:42] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 02:09:42] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 02:09:42] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 02:09:42] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_aV0hoFuayAItGOxBHs6x0TCzhW0gpOab"} 
[2025-08-22 02:09:42] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:38:19+08:00"},{"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T01:41:25+08:00"}]} 
[2025-08-22 02:09:42] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:09:42] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":0,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:09:42] production.INFO: WebSocket连接认证成功 {"session_id":"ws_aV0hoFuayAItGOxBHs6x0TCzhW0gpOab","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 02:09:57] production.INFO: WebSocket会话正常连接 {"session_id":"ws_aV0hoFuayAItGOxBHs6x0TCzhW0gpOab","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:10:42] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_i6WvoFKxaplzi6H9Db6OrB7gEpu6iUGX","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","original_status":"disconnected","cached_fd":"5","reason":"连接已断开，定时清理检测到无效连接"} 
[2025-08-22 02:10:42] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_6FAVvofohBxt5LT4QeCIh1aSFh0SaO4l","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","original_status":"disconnected","cached_fd":"7","reason":"连接已断开，定时清理检测到无效连接"} 
[2025-08-22 02:10:42] production.INFO: WebSocket会话状态清理完成 {"total_checked":5,"cleaned_count":2,"checked_statuses":["connected","pending"],"timestamp":"2025-08-22T02:10:42+08:00"} 
[2025-08-22 02:10:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:10:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:10:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799842.433911,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:10:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":14.5,"total_stack_cleared":true} 
[2025-08-22 02:10:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T02:10:42+08:00"} 
[2025-08-22 02:10:44] production.INFO: User offline {"user_id":14} 
[2025-08-22 02:10:56] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 02:10:56] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 02:10:56] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_QsR6FxxW46RpDN3bKvbrbMPbBr4uQ5HZ"} 
[2025-08-22 02:10:56] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 02:10:56] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:10:56] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:10:56] production.INFO: WebSocket连接认证成功 {"session_id":"ws_QsR6FxxW46RpDN3bKvbrbMPbBr4uQ5HZ","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 02:11:06] production.INFO: WebSocket会话正常连接 {"session_id":"ws_QsR6FxxW46RpDN3bKvbrbMPbBr4uQ5HZ","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:11:31] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 02:11:31] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 02:11:31] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_8xDKVyoQQrfeL2EzmNSBR5WQRAHBPQOT"} 
[2025-08-22 02:11:31] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_QsR6FxxW46RpDN3bKvbrbMPbBr4uQ5HZ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:11:06+08:00"}]} 
[2025-08-22 02:11:31] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:11:31] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:11:31] production.INFO: WebSocket连接认证成功 {"session_id":"ws_8xDKVyoQQrfeL2EzmNSBR5WQRAHBPQOT","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 02:11:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:11:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:11:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799902.303599,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:11:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":77.36,"total_stack_cleared":true} 
[2025-08-22 02:11:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T02:11:42+08:00"} 
[2025-08-22 02:11:42] production.INFO: WebSocket会话正常连接 {"session_id":"ws_8xDKVyoQQrfeL2EzmNSBR5WQRAHBPQOT","user_id":14,"fd":3,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:12:04] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-22 02:12:04] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"mystory","status":"allowed"} 
[2025-08-22 02:12:04] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"mystory","mapped_task_type":"text_generation_mystory","user_id":14,"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3"} 
[2025-08-22 02:12:04] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_mystory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_QsR6FxxW46RpDN3bKvbrbMPbBr4uQ5HZ","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:11:06+08:00"},{"session_id":"ws_8xDKVyoQQrfeL2EzmNSBR5WQRAHBPQOT","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:11:42+08:00"}]} 
[2025-08-22 02:12:04] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_mystory","frontend_business_type":"mystory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:12:04] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_mystory","frontend_business_type":"mystory","existing_same_type_connections":0,"existing_different_type_connections":2,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:12:04] production.INFO: WebSocket连接认证成功 {"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_mystory"} 
[2025-08-22 02:12:20] production.INFO: WebSocket会话正常连接 {"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","user_id":14,"fd":4,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:12:27] production.INFO: User offline {"user_id":14} 
[2025-08-22 02:12:41] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":2,"max_business_types":14,"existing_business_types":["text_generation_aistory","text_generation_mystory"]} 
[2025-08-22 02:12:41] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 02:12:41] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_H5Rj2lGbqUIkVqJ9wZGdr8E46ccJ6m0d"} 
[2025-08-22 02:12:41] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_8xDKVyoQQrfeL2EzmNSBR5WQRAHBPQOT","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:11:42+08:00"},{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"}]} 
[2025-08-22 02:12:41] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:12:41] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"existing_different_type_connections":1,"total_existing_connections":2,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:12:41] production.INFO: WebSocket连接认证成功 {"session_id":"ws_H5Rj2lGbqUIkVqJ9wZGdr8E46ccJ6m0d","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 02:12:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:12:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:12:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755799962.332076,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:12:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":8.79,"total_stack_cleared":true} 
[2025-08-22 02:12:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-22T02:12:42+08:00"} 
[2025-08-22 02:12:52] production.INFO: WebSocket会话正常连接 {"session_id":"ws_H5Rj2lGbqUIkVqJ9wZGdr8E46ccJ6m0d","user_id":14,"fd":5,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:13:20] production.WARNING: WebSocket连接错误 {"fd":6,"code":409,"message":"会话已在其他地方连接","error_message":{"type":"error","code":409,"message":"会话已在其他地方连接","data":{"timestamp":"2025-08-22T02:13:19+08:00","reason":"状态冲突","action":"请检查当前状态是否允许此操作"}}} 
[2025-08-22 02:13:33] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":3,"business_types_used":2,"max_business_types":14,"existing_business_types":["text_generation_aistory","text_generation_mystory"]} 
[2025-08-22 02:13:33] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":3,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 02:13:33] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_EjJNfTgEhRnhGvb0qNkLpEl65NeEzF9F"} 
[2025-08-22 02:13:33] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":3,"existing_sessions":[{"session_id":"ws_8xDKVyoQQrfeL2EzmNSBR5WQRAHBPQOT","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:11:42+08:00"},{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_H5Rj2lGbqUIkVqJ9wZGdr8E46ccJ6m0d","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:12:52+08:00"}]} 
[2025-08-22 02:13:33] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:13:33] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":1,"total_existing_connections":3,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:13:33] production.INFO: WebSocket连接认证成功 {"session_id":"ws_EjJNfTgEhRnhGvb0qNkLpEl65NeEzF9F","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 02:13:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:13:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:13:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800022.307599,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:13:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":28.98,"total_stack_cleared":true} 
[2025-08-22 02:13:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:13:42+08:00"} 
[2025-08-22 02:13:47] production.INFO: WebSocket会话正常连接 {"session_id":"ws_EjJNfTgEhRnhGvb0qNkLpEl65NeEzF9F","user_id":14,"fd":7,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:14:25] production.INFO: User offline {"user_id":14} 
[2025-08-22 02:14:28] production.WARNING: WebSocket连接错误 {"fd":8,"code":1110,"message":"Session已失效，请重新认证","error_message":{"type":"error","code":1110,"message":"Session已失效，请重新认证","data":{"timestamp":"2025-08-22T02:14:28+08:00","reason":"session_id冲突或已失效","action":"请重新获取有效的session_id并建立新连接"}}} 
[2025-08-22 02:14:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:14:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:14:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800082.343769,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:14:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":95.51,"total_stack_cleared":true} 
[2025-08-22 02:14:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":2,"timestamp":"2025-08-22T02:14:42+08:00"} 
[2025-08-22 02:14:45] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":3,"business_types_used":2,"max_business_types":14,"existing_business_types":["text_generation_mystory","text_generation_aistory"]} 
[2025-08-22 02:14:45] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":3,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-22 02:14:45] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_9f1DauFcHzkfxQyr45rdz6ltwKHex8o1"} 
[2025-08-22 02:14:45] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":3,"existing_sessions":[{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_H5Rj2lGbqUIkVqJ9wZGdr8E46ccJ6m0d","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:12:52+08:00"},{"session_id":"ws_EjJNfTgEhRnhGvb0qNkLpEl65NeEzF9F","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:13:47+08:00"}]} 
[2025-08-22 02:14:45] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:14:45] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":2,"existing_different_type_connections":1,"total_existing_connections":3,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:14:45] production.INFO: WebSocket连接认证成功 {"session_id":"ws_9f1DauFcHzkfxQyr45rdz6ltwKHex8o1","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_aistory"} 
[2025-08-22 02:14:57] production.INFO: WebSocket会话正常连接 {"session_id":"ws_9f1DauFcHzkfxQyr45rdz6ltwKHex8o1","user_id":14,"fd":9,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:15:34] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":4,"business_types_used":2,"max_business_types":14,"existing_business_types":["text_generation_mystory","text_generation_aistory"]} 
[2025-08-22 02:15:34] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":4,"max_connections":14,"frontend_business_type":"mystory","status":"allowed"} 
[2025-08-22 02:15:34] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"mystory","mapped_task_type":"text_generation_mystory","user_id":14,"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb"} 
[2025-08-22 02:15:34] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_mystory","existing_sessions_count":4,"existing_sessions":[{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_H5Rj2lGbqUIkVqJ9wZGdr8E46ccJ6m0d","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:12:52+08:00"},{"session_id":"ws_EjJNfTgEhRnhGvb0qNkLpEl65NeEzF9F","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:13:47+08:00"},{"session_id":"ws_9f1DauFcHzkfxQyr45rdz6ltwKHex8o1","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:14:57+08:00"}]} 
[2025-08-22 02:15:34] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_mystory","frontend_business_type":"mystory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:15:34] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_mystory","frontend_business_type":"mystory","existing_same_type_connections":1,"existing_different_type_connections":3,"total_existing_connections":4,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:15:34] production.INFO: WebSocket连接认证成功 {"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_generation_mystory"} 
[2025-08-22 02:15:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:15:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:15:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800142.309365,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:15:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":32.61,"total_stack_cleared":true} 
[2025-08-22 02:15:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":3,"timestamp":"2025-08-22T02:15:42+08:00"} 
[2025-08-22 02:15:42] production.INFO: WebSocket会话正常连接 {"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","user_id":14,"fd":10,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:16:07] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":5,"business_types_used":2,"max_business_types":14,"existing_business_types":["text_generation_mystory","text_generation_aistory"]} 
[2025-08-22 02:16:07] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":5,"max_connections":14,"frontend_business_type":"promptcharacter","status":"allowed"} 
[2025-08-22 02:16:07] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptcharacter","mapped_task_type":"text_prompt_character","user_id":14,"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe"} 
[2025-08-22 02:16:07] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_character","existing_sessions_count":5,"existing_sessions":[{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_H5Rj2lGbqUIkVqJ9wZGdr8E46ccJ6m0d","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:12:52+08:00"},{"session_id":"ws_EjJNfTgEhRnhGvb0qNkLpEl65NeEzF9F","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:13:47+08:00"},{"session_id":"ws_9f1DauFcHzkfxQyr45rdz6ltwKHex8o1","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:14:57+08:00"},{"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:15:42+08:00"}]} 
[2025-08-22 02:16:07] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_character","frontend_business_type":"promptcharacter","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:16:07] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_character","frontend_business_type":"promptcharacter","existing_same_type_connections":0,"existing_different_type_connections":5,"total_existing_connections":5,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:16:07] production.INFO: WebSocket连接认证成功 {"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_character"} 
[2025-08-22 02:16:20] production.INFO: WebSocket会话正常连接 {"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe","user_id":14,"fd":11,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:16:42] production.INFO: pending状态会话超时 {"session_id":"ws_x31W6QOM22vKYHo3lr2CHYdw5pwpar9C","user_id":14,"created_at":"2025-08-22 02:06:06","timeout_minutes":10,"reason":"pending状态超过10分钟未连接"} 
[2025-08-22 02:16:42] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_x31W6QOM22vKYHo3lr2CHYdw5pwpar9C","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","original_status":"disconnected","cached_fd":null,"reason":"pending状态超时，认证后未及时建立连接"} 
[2025-08-22 02:16:42] production.INFO: WebSocket会话状态清理完成 {"total_checked":8,"cleaned_count":1,"checked_statuses":["connected","pending"],"timestamp":"2025-08-22T02:16:42+08:00"} 
[2025-08-22 02:16:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:16:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:16:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800202.496893,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:16:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":16.67,"total_stack_cleared":true} 
[2025-08-22 02:16:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":4,"updated_sessions":4,"timestamp":"2025-08-22T02:16:42+08:00"} 
[2025-08-22 02:16:49] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":6,"business_types_used":3,"max_business_types":14,"existing_business_types":["text_generation_mystory","text_generation_aistory","text_prompt_character"]} 
[2025-08-22 02:16:49] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":6,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:16:49] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_mCW4V4zhhaqrlJi87B56bcf3Hzspc47o"} 
[2025-08-22 02:16:49] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":6,"existing_sessions":[{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_H5Rj2lGbqUIkVqJ9wZGdr8E46ccJ6m0d","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:12:52+08:00"},{"session_id":"ws_EjJNfTgEhRnhGvb0qNkLpEl65NeEzF9F","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:13:47+08:00"},{"session_id":"ws_9f1DauFcHzkfxQyr45rdz6ltwKHex8o1","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:14:57+08:00"},{"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:15:42+08:00"},{"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe","business_type":"text_prompt_character","status":"connected","connected_at":"2025-08-22T02:16:20+08:00"}]} 
[2025-08-22 02:16:49] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:16:49] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":0,"existing_different_type_connections":6,"total_existing_connections":6,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:16:49] production.INFO: WebSocket连接认证成功 {"session_id":"ws_mCW4V4zhhaqrlJi87B56bcf3Hzspc47o","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
[2025-08-22 02:16:58] production.INFO: WebSocket会话正常连接 {"session_id":"ws_mCW4V4zhhaqrlJi87B56bcf3Hzspc47o","user_id":14,"fd":12,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:17:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:17:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:17:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800262.360622,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:17:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":41.96,"total_stack_cleared":true} 
[2025-08-22 02:17:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:17:42+08:00"} 
[2025-08-22 02:17:42] production.INFO: pending状态会话超时 {"session_id":"ws_PNbR3X0xhap77rHQTQxuWuYKtZ3tWQ97","user_id":14,"created_at":"2025-08-22 02:06:51","timeout_minutes":10,"reason":"pending状态超过10分钟未连接"} 
[2025-08-22 02:17:42] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_PNbR3X0xhap77rHQTQxuWuYKtZ3tWQ97","user_id":14,"business_type":"text_generation_aistory","client_type":"python_tool","original_status":"disconnected","cached_fd":null,"reason":"pending状态超时，认证后未及时建立连接"} 
[2025-08-22 02:17:42] production.INFO: WebSocket会话状态清理完成 {"total_checked":8,"cleaned_count":1,"checked_statuses":["connected","pending"],"timestamp":"2025-08-22T02:17:42+08:00"} 
[2025-08-22 02:18:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:18:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:18:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800322.381116,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:18:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":133.52,"total_stack_cleared":true} 
[2025-08-22 02:18:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:18:42+08:00"} 
[2025-08-22 02:19:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:19:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:19:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800382.333636,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:19:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":70.26,"total_stack_cleared":true} 
[2025-08-22 02:19:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:19:42+08:00"} 
[2025-08-22 02:20:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:20:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:20:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800442.39325,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:20:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":96.99,"total_stack_cleared":true} 
[2025-08-22 02:20:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:20:42+08:00"} 
[2025-08-22 02:21:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:21:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:21:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800502.346603,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:21:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":64.3,"total_stack_cleared":true} 
[2025-08-22 02:21:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:21:42+08:00"} 
[2025-08-22 02:22:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:22:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:22:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800562.368458,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:22:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":66.78,"total_stack_cleared":true} 
[2025-08-22 02:22:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:22:42+08:00"} 
[2025-08-22 02:22:52] production.INFO: User offline {"user_id":14} 
[2025-08-22 02:23:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:23:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:23:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800622.338111,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:23:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":50.23,"total_stack_cleared":true} 
[2025-08-22 02:23:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:23:42+08:00"} 
[2025-08-22 02:24:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:24:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:24:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800682.337992,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:24:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":123.58,"total_stack_cleared":true} 
[2025-08-22 02:24:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:24:42+08:00"} 
[2025-08-22 02:25:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:25:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:25:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800742.335703,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:25:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":134.55,"total_stack_cleared":true} 
[2025-08-22 02:25:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:25:42+08:00"} 
[2025-08-22 02:26:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:26:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:26:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800802.352274,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:26:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":85.9,"total_stack_cleared":true} 
[2025-08-22 02:26:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:26:42+08:00"} 
[2025-08-22 02:26:45] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":6,"business_types_used":4,"max_business_types":14,"existing_business_types":["text_generation_mystory","text_generation_aistory","text_prompt_character","text_prompt_style"]} 
[2025-08-22 02:26:45] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":6,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:26:45] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_HxlbL4qoRAcpVhR5gH7c3hC00aBTpUIK"} 
[2025-08-22 02:26:45] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":6,"existing_sessions":[{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_EjJNfTgEhRnhGvb0qNkLpEl65NeEzF9F","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:13:47+08:00"},{"session_id":"ws_9f1DauFcHzkfxQyr45rdz6ltwKHex8o1","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:14:57+08:00"},{"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:15:42+08:00"},{"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe","business_type":"text_prompt_character","status":"connected","connected_at":"2025-08-22T02:16:20+08:00"},{"session_id":"ws_mCW4V4zhhaqrlJi87B56bcf3Hzspc47o","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-22T02:16:58+08:00"}]} 
[2025-08-22 02:26:45] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:26:45] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"existing_different_type_connections":5,"total_existing_connections":6,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:26:45] production.INFO: WebSocket连接认证成功 {"session_id":"ws_HxlbL4qoRAcpVhR5gH7c3hC00aBTpUIK","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
[2025-08-22 02:27:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:27:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:27:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800862.308724,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:27:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":29.52,"total_stack_cleared":true} 
[2025-08-22 02:27:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:27:42+08:00"} 
[2025-08-22 02:28:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:28:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:28:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800922.350151,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:28:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":80.63,"total_stack_cleared":true} 
[2025-08-22 02:28:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":6,"updated_sessions":6,"timestamp":"2025-08-22T02:28:42+08:00"} 
[2025-08-22 02:28:47] production.INFO: User offline {"user_id":14} 
[2025-08-22 02:29:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:29:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:29:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755800982.328788,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:29:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":68.57,"total_stack_cleared":true} 
[2025-08-22 02:29:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":5,"updated_sessions":5,"timestamp":"2025-08-22T02:29:42+08:00"} 
[2025-08-22 02:30:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:30:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:30:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801042.35502,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:30:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":111.45,"total_stack_cleared":true} 
[2025-08-22 02:30:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":5,"updated_sessions":5,"timestamp":"2025-08-22T02:30:42+08:00"} 
[2025-08-22 02:31:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:31:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:31:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801102.313955,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:31:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":110.19,"total_stack_cleared":true} 
[2025-08-22 02:31:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":5,"updated_sessions":5,"timestamp":"2025-08-22T02:31:42+08:00"} 
[2025-08-22 02:32:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:32:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:32:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801162.344672,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:32:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":68.35,"total_stack_cleared":true} 
[2025-08-22 02:32:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":5,"updated_sessions":5,"timestamp":"2025-08-22T02:32:42+08:00"} 
[2025-08-22 02:32:44] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":5,"business_types_used":4,"max_business_types":14,"existing_business_types":["text_generation_mystory","text_generation_aistory","text_prompt_character","text_prompt_style"]} 
[2025-08-22 02:32:44] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":5,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:32:44] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_uc0VhLX9bnU0PAC1bOvOg6Drrb1raQhm"} 
[2025-08-22 02:32:44] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":5,"existing_sessions":[{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_9f1DauFcHzkfxQyr45rdz6ltwKHex8o1","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-22T02:14:57+08:00"},{"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:15:42+08:00"},{"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe","business_type":"text_prompt_character","status":"connected","connected_at":"2025-08-22T02:16:20+08:00"},{"session_id":"ws_mCW4V4zhhaqrlJi87B56bcf3Hzspc47o","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-22T02:16:58+08:00"}]} 
[2025-08-22 02:32:44] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:32:44] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"existing_different_type_connections":4,"total_existing_connections":5,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:32:44] production.INFO: WebSocket连接认证成功 {"session_id":"ws_uc0VhLX9bnU0PAC1bOvOg6Drrb1raQhm","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
[2025-08-22 02:33:09] production.INFO: User offline {"user_id":14} 
[2025-08-22 02:33:24] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":4,"business_types_used":3,"max_business_types":14,"existing_business_types":["text_generation_mystory","text_prompt_character","text_prompt_style"]} 
[2025-08-22 02:33:24] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":4,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:33:24] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_8qZFZRINq4oyA5OlgAnbHauVHUtkiMjE"} 
[2025-08-22 02:33:24] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":4,"existing_sessions":[{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:15:42+08:00"},{"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe","business_type":"text_prompt_character","status":"connected","connected_at":"2025-08-22T02:16:20+08:00"},{"session_id":"ws_mCW4V4zhhaqrlJi87B56bcf3Hzspc47o","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-22T02:16:58+08:00"}]} 
[2025-08-22 02:33:24] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:33:24] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"existing_different_type_connections":3,"total_existing_connections":4,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:33:24] production.INFO: WebSocket连接认证成功 {"session_id":"ws_8qZFZRINq4oyA5OlgAnbHauVHUtkiMjE","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
[2025-08-22 02:33:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:33:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:33:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801222.29522,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:33:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":55.8,"total_stack_cleared":true} 
[2025-08-22 02:33:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":4,"updated_sessions":4,"timestamp":"2025-08-22T02:33:42+08:00"} 
[2025-08-22 02:34:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:34:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:34:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801282.339446,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:34:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":48.45,"total_stack_cleared":true} 
[2025-08-22 02:34:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":4,"updated_sessions":4,"timestamp":"2025-08-22T02:34:42+08:00"} 
[2025-08-22 02:35:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:35:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:35:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801342.304768,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:35:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":40.18,"total_stack_cleared":true} 
[2025-08-22 02:35:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":4,"updated_sessions":4,"timestamp":"2025-08-22T02:35:42+08:00"} 
[2025-08-22 02:36:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:36:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:36:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801402.319464,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:36:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":116.47,"total_stack_cleared":true} 
[2025-08-22 02:36:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":4,"updated_sessions":4,"timestamp":"2025-08-22T02:36:42+08:00"} 
[2025-08-22 02:37:42] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1665,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:37:42] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:37:42] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1665,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801462.302144,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:37:42] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":129.42,"total_stack_cleared":true} 
[2025-08-22 02:37:42] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":4,"updated_sessions":4,"timestamp":"2025-08-22T02:37:42+08:00"} 
[2025-08-22 02:37:42] production.INFO: pending状态会话超时 {"session_id":"ws_HxlbL4qoRAcpVhR5gH7c3hC00aBTpUIK","user_id":14,"created_at":"2025-08-22 02:26:45","timeout_minutes":10,"reason":"pending状态超过10分钟未连接"} 
[2025-08-22 02:37:42] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_HxlbL4qoRAcpVhR5gH7c3hC00aBTpUIK","user_id":14,"business_type":"text_prompt_style","client_type":"python_tool","original_status":"disconnected","cached_fd":null,"reason":"pending状态超时，认证后未及时建立连接"} 
[2025-08-22 02:37:42] production.INFO: WebSocket会话状态清理完成 {"total_checked":7,"cleaned_count":1,"checked_statuses":["connected","pending"],"timestamp":"2025-08-22T02:37:42+08:00"} 
[2025-08-22 02:38:54] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 02:38:54] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 02:38:54] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 02:38:54] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 02:38:54] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 02:39:20] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":4,"business_types_used":3,"max_business_types":14,"existing_business_types":["text_generation_mystory","text_prompt_character","text_prompt_style"]} 
[2025-08-22 02:39:20] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":4,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:39:20] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_eRUNOiRhlNKTggMbS7oYIv1i9606nyFc"} 
[2025-08-22 02:39:20] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":4,"existing_sessions":[{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:15:42+08:00"},{"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe","business_type":"text_prompt_character","status":"connected","connected_at":"2025-08-22T02:16:20+08:00"},{"session_id":"ws_mCW4V4zhhaqrlJi87B56bcf3Hzspc47o","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-22T02:16:58+08:00"}]} 
[2025-08-22 02:39:20] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:39:20] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"existing_different_type_connections":3,"total_existing_connections":4,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:39:21] production.INFO: WebSocket连接认证成功 {"session_id":"ws_eRUNOiRhlNKTggMbS7oYIv1i9606nyFc","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
[2025-08-22 02:39:28] production.INFO: WebSocket会话正常连接 {"session_id":"ws_eRUNOiRhlNKTggMbS7oYIv1i9606nyFc","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:39:41] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":5,"business_types_used":3,"max_business_types":14,"existing_business_types":["text_generation_mystory","text_prompt_character","text_prompt_style"]} 
[2025-08-22 02:39:41] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":5,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:39:41] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_kVsW2x66h8wCLTSWKUQihP7q8G6lJsww"} 
[2025-08-22 02:39:41] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":5,"existing_sessions":[{"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:12:20+08:00"},{"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-22T02:15:42+08:00"},{"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe","business_type":"text_prompt_character","status":"connected","connected_at":"2025-08-22T02:16:20+08:00"},{"session_id":"ws_mCW4V4zhhaqrlJi87B56bcf3Hzspc47o","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-22T02:16:58+08:00"},{"session_id":"ws_eRUNOiRhlNKTggMbS7oYIv1i9606nyFc","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-22T02:39:28+08:00"}]} 
[2025-08-22 02:39:41] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:39:41] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":2,"existing_different_type_connections":3,"total_existing_connections":5,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:39:41] production.INFO: WebSocket连接认证成功 {"session_id":"ws_kVsW2x66h8wCLTSWKUQihP7q8G6lJsww","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
[2025-08-22 02:39:55] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_MFbtEBUJzGNZT0LYEsuSrxqE4np2igG3","user_id":14,"business_type":"text_generation_mystory","client_type":"python_tool","original_status":"disconnected","cached_fd":"4","last_ping_at":"2025-08-22 02:37:11","last_ping_minutes_ago":2,"reason":"连接已断开，定时清理检测到无效连接"} 
[2025-08-22 02:39:55] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_3qQfqVtrKTOaR0mklwzzexq4BbYrvRDb","user_id":14,"business_type":"text_generation_mystory","client_type":"python_tool","original_status":"disconnected","cached_fd":"10","last_ping_at":"2025-08-22 02:37:11","last_ping_minutes_ago":2,"reason":"连接已断开，定时清理检测到无效连接"} 
[2025-08-22 02:39:55] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_zg4sU2P2j4sURGnyDzMw7uuFZPo38WOe","user_id":14,"business_type":"text_prompt_character","client_type":"python_tool","original_status":"disconnected","cached_fd":"11","last_ping_at":"2025-08-22 02:37:11","last_ping_minutes_ago":2,"reason":"连接已断开，定时清理检测到无效连接"} 
[2025-08-22 02:39:55] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_mCW4V4zhhaqrlJi87B56bcf3Hzspc47o","user_id":14,"business_type":"text_prompt_style","client_type":"python_tool","original_status":"disconnected","cached_fd":"12","last_ping_at":"2025-08-22 02:37:11","last_ping_minutes_ago":2,"reason":"连接已断开，定时清理检测到无效连接"} 
[2025-08-22 02:39:55] production.INFO: WebSocket会话心跳超时 {"session_id":"ws_eRUNOiRhlNKTggMbS7oYIv1i9606nyFc","user_id":14,"fd":"1","last_ping_at":"2025-08-22 02:39:28","reason":"心跳超时，超过5分钟无心跳"} 
[2025-08-22 02:39:55] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_eRUNOiRhlNKTggMbS7oYIv1i9606nyFc","user_id":14,"business_type":"text_prompt_style","client_type":"python_tool","original_status":"disconnected","cached_fd":"1","last_ping_at":"2025-08-22 02:39:28","last_ping_minutes_ago":0,"reason":"心跳超时，超过5分钟无心跳信号"} 
[2025-08-22 02:39:55] production.WARNING: WebSocket数据不一致检测 {"actual_connections":1,"cache_connections":1,"valid_cache_connections":1,"db_active_sessions":8,"cleaned_sessions":5,"db_status_distribution":{"pending":3,"disconnected":23},"cache_stats":{"note":"缓存统计需要根据具体缓存实现来完善"},"inconsistency_detected":true} 
[2025-08-22 02:39:55] production.INFO: WebSocket会话状态清理完成 {"total_checked":8,"cleaned_count":5,"checked_statuses":["connected","pending"],"timestamp":"2025-08-22T02:39:55+08:00"} 
[2025-08-22 02:40:00] production.INFO: WebSocket会话正常连接 {"session_id":"ws_kVsW2x66h8wCLTSWKUQihP7q8G6lJsww","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:40:26] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_prompt_style"]} 
[2025-08-22 02:40:26] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:40:26] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_IdHDeISGn8vxMlnAi7P4cN4WxeirCWdm"} 
[2025-08-22 02:40:26] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_kVsW2x66h8wCLTSWKUQihP7q8G6lJsww","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-22T02:40:00+08:00"}]} 
[2025-08-22 02:40:26] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:40:26] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:40:26] production.INFO: WebSocket连接认证成功 {"session_id":"ws_IdHDeISGn8vxMlnAi7P4cN4WxeirCWdm","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
[2025-08-22 02:40:36] production.INFO: WebSocket会话正常连接 {"session_id":"ws_IdHDeISGn8vxMlnAi7P4cN4WxeirCWdm","user_id":14,"fd":3,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:40:54] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":333,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:40:54] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:40:55] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":333,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801654.990972,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:40:55] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":51.09,"total_stack_cleared":true} 
[2025-08-22 02:40:55] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":1,"timestamp":"2025-08-22T02:40:55+08:00"} 
[2025-08-22 02:40:55] production.INFO: WebSocket会话心跳超时 {"session_id":"ws_IdHDeISGn8vxMlnAi7P4cN4WxeirCWdm","user_id":14,"fd":"3","last_ping_at":"2025-08-22 02:40:36","reason":"心跳超时，超过5分钟无心跳"} 
[2025-08-22 02:40:55] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_IdHDeISGn8vxMlnAi7P4cN4WxeirCWdm","user_id":14,"business_type":"text_prompt_style","client_type":"python_tool","original_status":"disconnected","cached_fd":"3","last_ping_at":"2025-08-22 02:40:36","last_ping_minutes_ago":0,"reason":"心跳超时，超过5分钟无心跳信号"} 
[2025-08-22 02:40:55] production.WARNING: WebSocket数据不一致检测 {"actual_connections":3,"cache_connections":3,"valid_cache_connections":3,"db_active_sessions":4,"cleaned_sessions":1,"db_status_distribution":{"pending":2,"connected":1,"disconnected":24},"cache_stats":{"note":"缓存统计需要根据具体缓存实现来完善"},"inconsistency_detected":true} 
[2025-08-22 02:40:55] production.INFO: WebSocket会话状态清理完成 {"total_checked":4,"cleaned_count":1,"checked_statuses":["connected","pending"],"timestamp":"2025-08-22T02:40:55+08:00"} 
[2025-08-22 02:41:55] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":333,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":226,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:41:55] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-22 02:41:55] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":333,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755801715.04692,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":239,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":284,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-22 02:41:55] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":54.42,"total_stack_cleared":true} 
[2025-08-22 02:41:55] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":1,"timestamp":"2025-08-22T02:41:55+08:00"} 
[2025-08-22 02:42:22] production.INFO: User offline {"user_id":14} 
[2025-08-22 02:42:23] production.INFO: User offline {"user_id":14} 
[2025-08-22 02:42:25] production.INFO: User offline {"user_id":14} 
[2025-08-22 02:43:18] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-22 02:43:18] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-22 02:43:18] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-22 02:43:18] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-22 02:43:18] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-22 02:43:37] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-22 02:43:37] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:43:37] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_s0na7HZVQzGPb77jojaVK9iAtlX66ohM"} 
[2025-08-22 02:43:37] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-22 02:43:37] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:43:37] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":0,"existing_different_type_connections":0,"total_existing_connections":0,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:43:37] production.INFO: WebSocket连接认证成功 {"session_id":"ws_s0na7HZVQzGPb77jojaVK9iAtlX66ohM","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
[2025-08-22 02:43:51] production.INFO: WebSocket会话正常连接 {"session_id":"ws_s0na7HZVQzGPb77jojaVK9iAtlX66ohM","user_id":14,"fd":1,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:44:11] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_prompt_style"]} 
[2025-08-22 02:44:11] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:44:11] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_p8Ivm5AQGsR9hi0CD457MsK5BVKr2h7o"} 
[2025-08-22 02:44:11] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_s0na7HZVQzGPb77jojaVK9iAtlX66ohM","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-22T02:43:51+08:00"}]} 
[2025-08-22 02:44:11] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:44:11] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:44:11] production.INFO: WebSocket连接认证成功 {"session_id":"ws_p8Ivm5AQGsR9hi0CD457MsK5BVKr2h7o","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
[2025-08-22 02:44:18] production.INFO: WebSocket会话心跳超时 {"session_id":"ws_s0na7HZVQzGPb77jojaVK9iAtlX66ohM","user_id":14,"fd":"1","last_ping_at":"2025-08-22 02:43:51","reason":"心跳超时，超过5分钟无心跳"} 
[2025-08-22 02:44:18] production.INFO: 清理无效WebSocket会话 {"session_id":"ws_s0na7HZVQzGPb77jojaVK9iAtlX66ohM","user_id":14,"business_type":"text_prompt_style","client_type":"python_tool","original_status":"disconnected","cached_fd":"1","last_ping_at":"2025-08-22 02:43:51","last_ping_minutes_ago":0,"reason":"心跳超时，超过5分钟无心跳信号"} 
[2025-08-22 02:44:18] production.WARNING: WebSocket数据不一致检测 {"actual_connections":1,"cache_connections":1,"valid_cache_connections":1,"db_active_sessions":2,"cleaned_sessions":1,"db_status_distribution":{"pending":1,"disconnected":1},"cache_stats":{"note":"缓存统计需要根据具体缓存实现来完善"},"inconsistency_detected":true} 
[2025-08-22 02:44:18] production.INFO: WebSocket会话状态清理完成 {"total_checked":2,"cleaned_count":1,"checked_statuses":["connected","pending"],"timestamp":"2025-08-22T02:44:18+08:00"} 
[2025-08-22 02:44:19] production.INFO: WebSocket会话正常连接 {"session_id":"ws_p8Ivm5AQGsR9hi0CD457MsK5BVKr2h7o","user_id":14,"fd":2,"old_status":"pending","reason":"认证后正常建立连接，状态从pending转为connected"} 
[2025-08-22 02:44:50] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_prompt_style"]} 
[2025-08-22 02:44:50] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-22 02:44:50] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_u1j5blqMUdnLqYvzy7gjuhx6UR33PiEx"} 
[2025-08-22 02:44:50] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_p8Ivm5AQGsR9hi0CD457MsK5BVKr2h7o","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-22T02:44:19+08:00"}]} 
[2025-08-22 02:44:50] production.INFO: WebSocket并发连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"支持同用户多业务类型并发连接"} 
[2025-08-22 02:44:50] production.INFO: WebSocket并发连接支持 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":1,"existing_different_type_connections":0,"total_existing_connections":1,"reason":"支持同用户多业务类型并发连接，不自动断开其他业务类型的连接"} 
[2025-08-22 02:44:51] production.INFO: WebSocket连接认证成功 {"session_id":"ws_u1j5blqMUdnLqYvzy7gjuhx6UR33PiEx","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","business_type":"text_prompt_style"} 
