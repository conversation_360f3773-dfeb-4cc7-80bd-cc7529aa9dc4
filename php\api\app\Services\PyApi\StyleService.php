<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\StyleLibrary;
use Illuminate\Support\Facades\Log;

/**
 * 剧情风格管理服务
 */
class StyleService
{
    /**
     * 创建新风格
     */
    public function createStyle(array $styleData): array
    {
        try {
            $style = StyleLibrary::create($styleData);

            Log::info('剧情风格创建成功', [
                'style_id' => $style->id,
                'name' => $style->name,
                'status' => $style->status
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '风格创建成功',
                'data' => [
                    'id' => $style->id,
                    'name' => $style->name,
                    'status' => $style->status
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'name' => $styleData['name'] ?? null,
                'status' => $styleData['status'] ?? 'draft',
                'description' => $styleData['description'] ?? null,
            ];

            Log::error('剧情风格创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '风格创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 更新风格评分
     */
    public function updateRating(int $styleId, float $rating): array
    {
        try {
            $style = StyleLibrary::findOrFail($styleId);
            $style->updateRating($rating);

            Log::info('风格评分更新成功', [
                'style_id' => $styleId,
                'old_rating' => $style->rating,
                'new_rating' => $rating
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '评分更新成功',
                'data' => [
                    'style_id' => $styleId,
                    'new_rating' => $rating
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'style_id' => $styleId,
                'rating' => $rating,
            ];

            Log::error('风格评分更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '评分更新失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取推荐风格
     */
    public function getRecommendedStyles(int $userId, int $limit = 10): array
    {
        try {
            // 这里可以根据用户的历史使用记录进行个性化推荐
            // 目前先返回热门风格
            $styles = StyleLibrary::active()
                ->popular($limit)
                ->get(['id', 'name', 'description', 'thumbnail', 'rating', 'usage_count']);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $styles->toArray()
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'limit' => $limit,
            ];

            Log::error('获取推荐风格失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取推荐风格失败',
                'data' => null
            ];
        }
    }

    /**
     * 搜索风格
     */
    public function searchStyles(string $keyword, array $filters = []): array
    {
        try {
            $query = StyleLibrary::active();

            // 关键词搜索
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%")
                      ->orWhereJsonContains('tags', $keyword);
                });
            }

            // 分类筛选已移除（category字段已删除）
            // if (!empty($filters['category'])) {
            //     $query->byCategory($filters['category']);
            // }

            // 高级风格筛选
            if (isset($filters['is_premium'])) {
                if ($filters['is_premium']) {
                    $query->premium();
                } else {
                    $query->free();
                }
            }

            // 评分筛选
            if (!empty($filters['min_rating'])) {
                $query->where('rating', '>=', $filters['min_rating']);
            }

            $styles = $query->ordered()
                ->get(['id', 'name', 'description', 'thumbnail', 'is_premium', 'rating', 'usage_count', 'tags']);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'keyword' => $keyword,
                    'total' => $styles->count(),
                    'styles' => $styles->toArray()
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'keyword' => $keyword,
                'filters' => $filters,
            ];

            Log::error('搜索风格失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '搜索失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取风格标签统计（替代分类统计）
     */
    public function getTagStats(): array
    {
        try {
            // 由于删除了category字段，改为基于tags的统计
            $styles = StyleLibrary::active()
                ->whereNotNull('tags')
                ->get(['tags', 'rating', 'usage_count']);

            $tagStats = [];
            foreach ($styles as $style) {
                $tags = $style->tags ?? [];
                foreach ($tags as $tag) {
                    if (!isset($tagStats[$tag])) {
                        $tagStats[$tag] = [
                            'tag' => $tag,
                            'count' => 0,
                            'total_rating' => 0,
                            'total_usage' => 0
                        ];
                    }
                    $tagStats[$tag]['count']++;
                    $tagStats[$tag]['total_rating'] += $style->rating ?? 0;
                    $tagStats[$tag]['total_usage'] += $style->usage_count ?? 0;
                }
            }

            // 计算平均评分并排序
            $result = array_map(function($stat) {
                return [
                    'tag' => $stat['tag'],
                    'count' => $stat['count'],
                    'avg_rating' => $stat['count'] > 0 ? round($stat['total_rating'] / $stat['count'], 2) : 0,
                    'total_usage' => $stat['total_usage']
                ];
            }, $tagStats);

            // 按使用次数排序
            usort($result, function($a, $b) {
                return $b['total_usage'] - $a['total_usage'];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $result
            ];

        } catch (\Exception $e) {
            $error_context = [];

            Log::error('获取风格标签统计失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取统计信息失败',
                'data' => null
            ];
        }
    }

    /**
     * 更新风格数据（基于解析后的风格数据数组）
     * 使用事务管理确保数据一致性
     *
     * @param int $styleId 风格ID
     * @param array $styleData 解析后的风格数据数组
     * @param int $userId 用户ID（用于日志记录）
     * @return array 处理结果
     */
    public function updateAiStyleFromData(int $styleId, array $styleData, int $userId): array
    {
        try {
            // 开始事务
            \App\Services\Common\TransactionManager::begin('StyleService.updateAiStyleFromData');

            // 验证风格是否存在
            $style = StyleLibrary::find($styleId);
            if (!$style) {
                \App\Services\Common\TransactionManager::rollback('StyleService.updateAiStyleFromData', '指定的风格不存在');
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '指定的风格不存在',
                    'data' => []
                ];
            }

            // 验证风格数据格式
            if (!$this->validateAiStyleData($styleData)) {
                \App\Services\Common\TransactionManager::rollback('StyleService.updateAiStyleFromData', '风格数据格式无效');

                Log::warning('风格数据格式无效', [
                    'user_id' => $userId,
                    'style_id' => $styleId,
                    'style_data' => $styleData
                ]);

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '风格数据格式无效',
                    'data' => []
                ];
            }

            // 准备更新数据
            $updateData = [];

            // 更新风格名称
            if (isset($styleData['风格名称']) && !empty($styleData['风格名称'])) {
                $updateData['name'] = $styleData['风格名称'];
            }

            // 更新风格描述
            if (isset($styleData['风格描述']) && !empty($styleData['风格描述'])) {
                $updateData['description'] = $styleData['风格描述'];
            }

            // 更新AI提示词
            if (isset($styleData['提示词']) && !empty($styleData['提示词'])) {
                $updateData['ai_prompt'] = $styleData['提示词'];
            }

            // 处理 TAG 标签
            if (isset($styleData['TAG']) && is_array($styleData['TAG'])) {
                $updateData['tags'] = $styleData['TAG'];
            }

            // 如果没有需要更新的数据，直接返回成功
            if (empty($updateData)) {
                \App\Services\Common\TransactionManager::commit('StyleService.updateAiStyleFromData');

                Log::info('风格数据无需更新', [
                    'user_id' => $userId,
                    'style_id' => $styleId,
                    'style_name' => $style->name,
                    'style_data' => $styleData
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '风格数据无需更新',
                    'data' => [
                        'style_id' => $styleId,
                        'style_name' => $style->name,
                        'updated_data' => [],
                        'parsed_style_data' => $styleData
                    ]
                ];
            }

            // 更新风格数据
            $style->update($updateData);

            // 提交事务
            \App\Services\Common\TransactionManager::commit('StyleService.updateAiStyleFromData');

            Log::info('风格数据更新成功', [
                'user_id' => $userId,
                'style_id' => $styleId,
                'style_name' => $style->name,
                'updated_fields' => array_keys($updateData),
                'parsed_data' => $styleData
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '风格数据更新成功',
                'data' => [
                    'style_id' => $styleId,
                    'style_name' => $style->name,
                    'updated_data' => $updateData,
                    'parsed_style_data' => $styleData
                ]
            ];

        } catch (\Exception $e) {
            \App\Services\Common\TransactionManager::rollback('StyleService.updateAiStyleFromData', $e->getMessage());

            $error_context = [
                'user_id' => $userId,
                'style_id' => $styleId,
                'style_data' => $styleData
            ];

            Log::error('风格数据更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '风格数据更新失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 验证风格数据格式
     *
     * @param array $styleData 风格数据数组
     * @return bool 验证结果
     */
    private function validateAiStyleData(array $styleData): bool
    {
        try {
            // 验证必需字段
            $requiredFields = ['风格名称', '风格描述', '提示词'];
            foreach ($requiredFields as $field) {
                if (!isset($styleData[$field]) || empty($styleData[$field])) {
                    Log::warning('风格数据缺少必需字段', [
                        'missing_field' => $field,
                        'data' => $styleData
                    ]);
                    return false;
                }
            }

            // 验证TAG字段（可选）
            if (isset($styleData['TAG'])) {
                if (!is_array($styleData['TAG'])) {
                    Log::warning('风格TAG字段必须是数组', [
                        'tag_value' => $styleData['TAG'],
                        'tag_type' => gettype($styleData['TAG'])
                    ]);
                    return false;
                }
            }

            return true;

        } catch (\Exception $e) {
            Log::error('风格数据验证异常', [
                'error' => $e->getMessage(),
                'data' => $styleData
            ]);
            return false;
        }
    }

    /**
     * 验证风格是否存在且激活
     *
     * @param int $styleId 风格ID
     * @return array 验证结果
     */
    public function validateStyleExists(int $styleId): array
    {
        try {
            $style = StyleLibrary::where('id', $styleId)
                ->where('status', 'draft')
                ->first();

            if (!$style) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '指定的风格不存在或已被禁用',
                    'data' => []
                ];
            }

            Log::info('风格验证成功', [
                'style_id' => $styleId,
                'style_name' => $style->name
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '风格验证成功',
                'data' => [
                    'style' => $style
                ]
            ];

        } catch (\Exception $e) {
            Log::error('风格验证失败', [
                'method' => __METHOD__,
                'style_id' => $styleId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '风格验证失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }
}
