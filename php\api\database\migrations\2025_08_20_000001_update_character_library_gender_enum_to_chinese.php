<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 修改角色库表gender字段枚举定义为中文
 * 
 * 修改内容：
 * - 将gender字段的枚举定义从英文改为中文
 * - 从 ['male', 'female', 'other'] 改为 ['男性', '女性', '未知']
 * - 与category字段保持一致的中文枚举类型
 * 
 * 注意：数据已经在之前的迁移中更新为中文，这里只修改字段定义
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 第一步：清理和标准化现有数据
        // 将英文值转换为中文
        DB::table('character_library')->where('gender', 'male')->update(['gender' => '男性']);
        DB::table('character_library')->where('gender', 'female')->update(['gender' => '女性']);
        DB::table('character_library')->where('gender', 'other')->update(['gender' => '未知']);

        // 将无效值设置为默认值
        DB::table('character_library')
            ->whereNotIn('gender', ['男性', '女性', '未知'])
            ->whereNotNull('gender')
            ->update(['gender' => '未知']);

        // 第二步：修改字段类型为枚举
        DB::statement("ALTER TABLE `p_character_library` MODIFY COLUMN `gender` ENUM('男性', '女性', '未知') NULL COMMENT '性别'");

        // 记录迁移日志
        Log::info('角色库gender字段枚举定义中文化完成', [
            'migration' => 'UpdateCharacterLibraryGenderEnumToChinese',
            'table' => 'p_character_library',
            'field' => 'gender',
            'old_type' => 'varchar(20)',
            'new_type' => "ENUM('男性', '女性', '未知')",
            'data_cleanup' => [
                'male' => '男性',
                'female' => '女性',
                'other' => '未知',
                'invalid_values' => '未知'
            ],
            'note' => '字段类型已从varchar更新为枚举，数据已清理并标准化'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 回滚操作：先改回varchar类型，再转换数据
        DB::statement("ALTER TABLE `p_character_library` MODIFY COLUMN `gender` VARCHAR(20) NULL COMMENT '性别'");

        // 将中文值转换回英文
        DB::table('character_library')->where('gender', '男性')->update(['gender' => 'male']);
        DB::table('character_library')->where('gender', '女性')->update(['gender' => 'female']);
        DB::table('character_library')->where('gender', '未知')->update(['gender' => 'other']);

        // 记录回滚日志
        Log::info('角色库gender字段枚举定义中文化回滚完成', [
            'migration' => 'UpdateCharacterLibraryGenderEnumToChinese',
            'table' => 'p_character_library',
            'field' => 'gender',
            'rollback_type' => 'varchar(20)',
            'data_rollback' => [
                '男性' => 'male',
                '女性' => 'female',
                '未知' => 'other'
            ],
            'note' => '字段类型已回滚为varchar，数据已转换回英文'
        ]);
    }
};
