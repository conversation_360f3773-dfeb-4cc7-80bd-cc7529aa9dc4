<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建AI生成任务表
 *
 * 🔧 整合说明：
 * ✅ 整合了所有 ai_generation_tasks 相关的迁移修改
 * ✅ 包含 transaction_id 字段（积分交易ID）
 * ✅ 包含 info_data 字段（项目相关数据）
 * ✅ 使用 info_id 替代 project_id（信息ID）
 * ✅ 完整的字段定义和索引
 *
 * 📝 字段说明：
 * - info_id: 信息ID，关联相关信息表（原 project_id）
 * - info_data: 项目相关数据（storyboard_id、aspect_ratio、style_id等）
 * - transaction_id: 积分交易ID，关联p_points_transactions表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_generation_tasks', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->unsignedBigInteger('info_id')->nullable()->comment('信息ID，关联相关信息表');
            $table->json('info_data')->nullable()->comment('项目相关数据（JSON格式）');
            $table->unsignedBigInteger('model_config_id')->nullable()->comment('模型配置ID，关联p_ai_model_configs表');
            $table->string('task_type', 50)->comment('任务类型（text/image/video/voice/music/sound）');
            $table->string('platform', 50)->comment('AI平台标识');
            $table->string('model_name', 100)->comment('模型名称');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'timeout'])->default('pending')->comment('任务状态');
            $table->json('input_data')->nullable()->comment('输入数据');
            $table->json('output_data')->nullable()->comment('输出数据');
            $table->json('generation_params')->nullable()->comment('生成参数');
            $table->string('external_task_id', 255)->nullable()->comment('外部任务ID（AI平台返回的任务ID）');
            $table->unsignedBigInteger('transaction_id')->nullable()->comment('积分交易ID，关联p_points_transactions表');
            $table->decimal('cost', 8, 4)->default(0.0000)->comment('任务成本');
            $table->integer('tokens_used')->default(0)->comment('使用的令牌数');
            $table->integer('processing_time_ms')->nullable()->comment('处理时间（毫秒）');
            $table->timestamp('started_at')->nullable()->comment('开始时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->json('metadata')->nullable()->comment('任务元数据');
            $table->integer('retry_count')->default(0)->comment('重试次数');
            $table->integer('max_retries')->default(3)->comment('最大重试次数');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'status'], 'idx_user_status');
            $table->index(['task_type', 'status'], 'idx_type_status');
            $table->index(['platform', 'status'], 'idx_platform_status');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index(['started_at', 'status'], 'idx_started_status');
            $table->index(['completed_at', 'status'], 'idx_completed_status');
            $table->index('external_task_id', 'idx_external_task_id');
            $table->index('transaction_id', 'idx_transaction_id');
            $table->index('info_id', 'idx_info_id');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->comment('AI生成任务表 - 管理所有AI生成任务的状态和结果');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_generation_tasks');
    }
};
