<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->string('queue')->index()->comment('队列名称');
            $table->longText('payload')->comment('任务载荷数据');
            $table->unsignedTinyInteger('attempts')->comment('尝试次数');
            $table->unsignedInteger('reserved_at')->nullable()->comment('保留时间戳');
            $table->unsignedInteger('available_at')->comment('可用时间戳');
            $table->unsignedInteger('created_at')->comment('创建时间戳');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jobs');
    }
};
